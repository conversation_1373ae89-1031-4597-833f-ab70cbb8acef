/**
 * 编辑器集成接口
 * 提供视觉脚本节点与编辑器的集成功能
 */
import { NodeRegistry, NodeInfo, NodeCategory } from '../registry/NodeRegistry';
import { VisualScriptNode } from '../visualscript/VisualScriptNode';
import { Debug } from '../utils/Debug';

/**
 * 节点UI配置接口
 */
export interface NodeUIConfig {
  position: { x: number; y: number };
  size: { width: number; height: number };
  collapsed: boolean;
  selected: boolean;
  color?: string;
  customStyle?: any;
}

/**
 * 连接信息接口
 */
export interface ConnectionInfo {
  id: string;
  sourceNodeId: string;
  sourcePortName: string;
  targetNodeId: string;
  targetPortName: string;
  color?: string;
  style?: 'solid' | 'dashed' | 'dotted';
}

/**
 * 编辑器状态接口
 */
export interface EditorState {
  nodes: Map<string, VisualScriptNode>;
  connections: Map<string, ConnectionInfo>;
  selectedNodes: Set<string>;
  selectedConnections: Set<string>;
  clipboard: any[];
  undoStack: any[];
  redoStack: any[];
  zoom: number;
  pan: { x: number; y: number };
}

/**
 * 编辑器事件接口
 */
export interface EditorEvent {
  type: string;
  data: any;
  timestamp: number;
}

/**
 * 编辑器集成管理器
 */
class EditorIntegrationManager {
  private editorState: EditorState;
  private eventListeners: Map<string, Function[]> = new Map();
  private nodeUIConfigs: Map<string, NodeUIConfig> = new Map();
  private debugMode: boolean = false;

  constructor() {
    this.editorState = {
      nodes: new Map(),
      connections: new Map(),
      selectedNodes: new Set(),
      selectedConnections: new Set(),
      clipboard: [],
      undoStack: [],
      redoStack: [],
      zoom: 1.0,
      pan: { x: 0, y: 0 }
    };
  }

  /**
   * 初始化编辑器集成
   */
  initialize(): void {
    // 初始化节点注册表
    NodeRegistry.initialize();
    
    // 设置默认事件监听器
    this.setupDefaultEventListeners();
    
    Debug.log('EditorIntegration', '编辑器集成初始化完成');
  }

  /**
   * 设置默认事件监听器
   */
  private setupDefaultEventListeners(): void {
    this.on('nodeCreated', (data) => {
      Debug.log('EditorIntegration', `节点创建: ${data.nodeId} (${data.nodeType})`);
    });

    this.on('nodeDeleted', (data) => {
      Debug.log('EditorIntegration', `节点删除: ${data.nodeId}`);
    });

    this.on('connectionCreated', (data) => {
      Debug.log('EditorIntegration', `连接创建: ${data.connectionId}`);
    });

    this.on('connectionDeleted', (data) => {
      Debug.log('EditorIntegration', `连接删除: ${data.connectionId}`);
    });
  }

  /**
   * 创建节点
   */
  createNode(nodeType: string, position: { x: number; y: number }, id?: string): VisualScriptNode | null {
    const node = NodeRegistry.createNode(nodeType, id);
    if (!node) {
      return null;
    }

    // 添加到编辑器状态
    this.editorState.nodes.set(node.id, node);

    // 设置UI配置
    const nodeInfo = NodeRegistry.getNodeInfo(nodeType);
    this.nodeUIConfigs.set(node.id, {
      position,
      size: { width: 200, height: 100 },
      collapsed: false,
      selected: false,
      color: nodeInfo?.color
    });

    // 触发事件
    this.emit('nodeCreated', {
      nodeId: node.id,
      nodeType: nodeType,
      position
    });

    return node;
  }

  /**
   * 删除节点
   */
  deleteNode(nodeId: string): boolean {
    const node = this.editorState.nodes.get(nodeId);
    if (!node) {
      return false;
    }

    // 删除相关连接
    const connectionsToDelete: string[] = [];
    for (const [connectionId, connection] of this.editorState.connections) {
      if (connection.sourceNodeId === nodeId || connection.targetNodeId === nodeId) {
        connectionsToDelete.push(connectionId);
      }
    }

    for (const connectionId of connectionsToDelete) {
      this.deleteConnection(connectionId);
    }

    // 从编辑器状态中移除
    this.editorState.nodes.delete(nodeId);
    this.nodeUIConfigs.delete(nodeId);
    this.editorState.selectedNodes.delete(nodeId);

    // 触发事件
    this.emit('nodeDeleted', { nodeId });

    return true;
  }

  /**
   * 创建连接
   */
  createConnection(
    sourceNodeId: string,
    sourcePortName: string,
    targetNodeId: string,
    targetPortName: string
  ): string | null {
    const sourceNode = this.editorState.nodes.get(sourceNodeId);
    const targetNode = this.editorState.nodes.get(targetNodeId);

    if (!sourceNode || !targetNode) {
      Debug.warn('EditorIntegration', '连接创建失败: 节点不存在');
      return null;
    }

    // 检查端口是否存在
    const sourcePort = sourceNode.getOutputPort(sourcePortName);
    const targetPort = targetNode.getInputPort(targetPortName);

    if (!sourcePort || !targetPort) {
      Debug.warn('EditorIntegration', '连接创建失败: 端口不存在');
      return null;
    }

    // 检查类型兼容性
    if (!this.arePortsCompatible(sourcePort.type, targetPort.type)) {
      Debug.warn('EditorIntegration', '连接创建失败: 端口类型不兼容');
      return null;
    }

    // 创建连接
    const connectionId = this.generateConnectionId();
    const connection: ConnectionInfo = {
      id: connectionId,
      sourceNodeId,
      sourcePortName,
      targetNodeId,
      targetPortName,
      color: this.getConnectionColor(sourcePort.type),
      style: 'solid'
    };

    this.editorState.connections.set(connectionId, connection);

    // 在节点中建立连接
    sourceNode.connectOutput(sourcePortName, targetNode, targetPortName);

    // 触发事件
    this.emit('connectionCreated', {
      connectionId,
      sourceNodeId,
      sourcePortName,
      targetNodeId,
      targetPortName
    });

    return connectionId;
  }

  /**
   * 删除连接
   */
  deleteConnection(connectionId: string): boolean {
    const connection = this.editorState.connections.get(connectionId);
    if (!connection) {
      return false;
    }

    // 在节点中断开连接
    const sourceNode = this.editorState.nodes.get(connection.sourceNodeId);
    if (sourceNode) {
      sourceNode.disconnectOutput(connection.sourcePortName, connection.targetNodeId, connection.targetPortName);
    }

    // 从编辑器状态中移除
    this.editorState.connections.delete(connectionId);
    this.editorState.selectedConnections.delete(connectionId);

    // 触发事件
    this.emit('connectionDeleted', { connectionId });

    return true;
  }

  /**
   * 选择节点
   */
  selectNode(nodeId: string, addToSelection: boolean = false): void {
    if (!addToSelection) {
      this.clearSelection();
    }

    this.editorState.selectedNodes.add(nodeId);
    
    const uiConfig = this.nodeUIConfigs.get(nodeId);
    if (uiConfig) {
      uiConfig.selected = true;
    }

    this.emit('nodeSelected', { nodeId, addToSelection });
  }

  /**
   * 取消选择节点
   */
  deselectNode(nodeId: string): void {
    this.editorState.selectedNodes.delete(nodeId);
    
    const uiConfig = this.nodeUIConfigs.get(nodeId);
    if (uiConfig) {
      uiConfig.selected = false;
    }

    this.emit('nodeDeselected', { nodeId });
  }

  /**
   * 清除选择
   */
  clearSelection(): void {
    for (const nodeId of this.editorState.selectedNodes) {
      const uiConfig = this.nodeUIConfigs.get(nodeId);
      if (uiConfig) {
        uiConfig.selected = false;
      }
    }

    this.editorState.selectedNodes.clear();
    this.editorState.selectedConnections.clear();

    this.emit('selectionCleared', {});
  }

  /**
   * 移动节点
   */
  moveNode(nodeId: string, position: { x: number; y: number }): void {
    const uiConfig = this.nodeUIConfigs.get(nodeId);
    if (uiConfig) {
      uiConfig.position = position;
      this.emit('nodeMoved', { nodeId, position });
    }
  }

  /**
   * 复制选中的节点
   */
  copySelectedNodes(): void {
    const selectedNodes = Array.from(this.editorState.selectedNodes);
    const clipboard: any[] = [];

    for (const nodeId of selectedNodes) {
      const node = this.editorState.nodes.get(nodeId);
      const uiConfig = this.nodeUIConfigs.get(nodeId);
      
      if (node && uiConfig) {
        clipboard.push({
          nodeType: node.nodeType,
          position: { ...uiConfig.position },
          properties: node.getProperties()
        });
      }
    }

    this.editorState.clipboard = clipboard;
    this.emit('nodesCopied', { count: clipboard.length });
  }

  /**
   * 粘贴节点
   */
  pasteNodes(offset: { x: number; y: number } = { x: 20, y: 20 }): VisualScriptNode[] {
    const pastedNodes: VisualScriptNode[] = [];

    for (const clipboardItem of this.editorState.clipboard) {
      const position = {
        x: clipboardItem.position.x + offset.x,
        y: clipboardItem.position.y + offset.y
      };

      const node = this.createNode(clipboardItem.nodeType, position);
      if (node) {
        // 恢复属性
        for (const [key, value] of Object.entries(clipboardItem.properties)) {
          node.setProperty(key, value);
        }
        pastedNodes.push(node);
      }
    }

    this.emit('nodesPasted', { count: pastedNodes.length });
    return pastedNodes;
  }

  /**
   * 执行脚本
   */
  executeScript(): void {
    try {
      // 找到所有入口节点（没有输入连接的节点）
      const entryNodes = this.findEntryNodes();
      
      // 执行脚本
      for (const node of entryNodes) {
        this.executeNode(node);
      }

      this.emit('scriptExecuted', { entryNodesCount: entryNodes.length });
      
    } catch (error) {
      Debug.error('EditorIntegration', '脚本执行失败', error);
      this.emit('scriptExecutionError', { error: error.message });
    }
  }

  /**
   * 查找入口节点
   */
  private findEntryNodes(): VisualScriptNode[] {
    const entryNodes: VisualScriptNode[] = [];
    const nodesWithInputs = new Set<string>();

    // 找到所有有输入连接的节点
    for (const connection of this.editorState.connections.values()) {
      nodesWithInputs.add(connection.targetNodeId);
    }

    // 找到没有输入连接的节点
    for (const [nodeId, node] of this.editorState.nodes) {
      if (!nodesWithInputs.has(nodeId)) {
        entryNodes.push(node);
      }
    }

    return entryNodes;
  }

  /**
   * 执行单个节点
   */
  private executeNode(node: VisualScriptNode): any {
    try {
      // 收集输入数据
      const inputs: any = {};
      
      for (const inputPort of node.getInputPorts()) {
        const connections = this.getConnectionsToPort(node.id, inputPort.name);
        if (connections.length > 0) {
          // 执行源节点并获取输出
          const connection = connections[0]; // 假设每个输入端口只有一个连接
          const sourceNode = this.editorState.nodes.get(connection.sourceNodeId);
          if (sourceNode) {
            const sourceOutput = this.executeNode(sourceNode);
            inputs[inputPort.name] = sourceOutput[connection.sourcePortName];
          }
        }
      }

      // 执行节点
      const output = node.execute(inputs);
      
      if (this.debugMode) {
        Debug.log('EditorIntegration', `节点执行: ${node.id} (${node.nodeType})`, { inputs, output });
      }

      return output;
      
    } catch (error) {
      Debug.error('EditorIntegration', `节点执行失败: ${node.id}`, error);
      throw error;
    }
  }

  /**
   * 获取连接到指定端口的连接
   */
  private getConnectionsToPort(nodeId: string, portName: string): ConnectionInfo[] {
    const connections: ConnectionInfo[] = [];
    
    for (const connection of this.editorState.connections.values()) {
      if (connection.targetNodeId === nodeId && connection.targetPortName === portName) {
        connections.push(connection);
      }
    }
    
    return connections;
  }

  /**
   * 检查端口兼容性
   */
  private arePortsCompatible(sourceType: string, targetType: string): boolean {
    // 简化的类型兼容性检查
    if (sourceType === targetType) return true;
    if (sourceType === 'any' || targetType === 'any') return true;
    if (sourceType === 'trigger' && targetType === 'trigger') return true;
    
    // 数值类型兼容性
    const numericTypes = ['number', 'float', 'int'];
    if (numericTypes.includes(sourceType) && numericTypes.includes(targetType)) {
      return true;
    }
    
    return false;
  }

  /**
   * 获取连接颜色
   */
  private getConnectionColor(portType: string): string {
    const colorMap: { [key: string]: string } = {
      'trigger': '#FF6B6B',
      'number': '#4ECDC4',
      'string': '#45B7D1',
      'boolean': '#96CEB4',
      'object': '#FFEAA7',
      'array': '#DDA0DD',
      'any': '#95A5A6'
    };
    
    return colorMap[portType] || '#95A5A6';
  }

  /**
   * 生成连接ID
   */
  private generateConnectionId(): string {
    return 'conn_' + Math.random().toString(36).substr(2, 9);
  }

  /**
   * 获取编辑器状态
   */
  getEditorState(): EditorState {
    return this.editorState;
  }

  /**
   * 获取节点UI配置
   */
  getNodeUIConfig(nodeId: string): NodeUIConfig | undefined {
    return this.nodeUIConfigs.get(nodeId);
  }

  /**
   * 设置调试模式
   */
  setDebugMode(enabled: boolean): void {
    this.debugMode = enabled;
    Debug.log('EditorIntegration', `调试模式${enabled ? '启用' : '禁用'}`);
  }

  /**
   * 获取节点库信息
   */
  getNodeLibrary(): { categories: any[]; totalNodes: number } {
    const categories = NodeRegistry.getCategoryInfo();
    const statistics = NodeRegistry.getStatistics();
    
    return {
      categories: categories.map(cat => ({
        ...cat,
        nodes: NodeRegistry.getNodesByCategory(cat.category)
      })),
      totalNodes: statistics.totalNodes
    };
  }

  // 事件系统
  on(event: string, callback: Function): void {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, []);
    }
    this.eventListeners.get(event)!.push(callback);
  }

  off(event: string, callback: Function): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      const index = listeners.indexOf(callback);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    }
  }

  private emit(event: string, data?: any): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      const eventData: EditorEvent = {
        type: event,
        data,
        timestamp: Date.now()
      };
      
      listeners.forEach(callback => {
        try {
          callback(eventData);
        } catch (error) {
          Debug.error('EditorIntegration', `事件回调执行失败: ${event}`, error);
        }
      });
    }
  }
}

// 导出单例实例
export const EditorIntegration = new EditorIntegrationManager();
