/**
 * 姿态检测节点
 * 使用MediaPipe进行人体姿态检测，集成真实MediaPipe库
 */
import { VisualScriptNode } from '../../../visualscript/VisualScriptNode';
import { Debug } from '../../../utils/Debug';

/**
 * 关键点数据接口
 */
export interface LandmarkData {
  x: number;
  y: number;
  z?: number;
  visibility?: number;
}

/**
 * 世界坐标关键点数据接口
 */
export interface WorldLandmarkData {
  x: number;
  y: number;
  z: number;
  visibility?: number;
}

/**
 * MediaPipe配置接口
 */
export interface MediaPipeConfig {
  modelComplexity: number;
  smoothLandmarks: boolean;
  enableSegmentation: boolean;
  minDetectionConfidence: number;
  minTrackingConfidence: number;
  enableWorldLandmarks: boolean;
  staticImageMode: boolean;
}

/**
 * 姿态检测结果接口
 */
export interface PoseResults {
  landmarks?: LandmarkData[];
  worldLandmarks?: WorldLandmarkData[];
  segmentationMask?: ImageData;
  confidence: number;
  processingTime: number;
  timestamp: number;
}

/**
 * 姿态稳定性滤波器
 */
class PoseStabilityFilter {
  private landmarkHistory: LandmarkData[][] = [];
  private maxHistorySize = 5;
  private smoothingFactor = 0.7;

  /**
   * 应用稳定性滤波
   */
  applyFilter(landmarks: LandmarkData[]): LandmarkData[] {
    if (!landmarks || landmarks.length === 0) {
      return landmarks;
    }

    // 添加到历史记录
    this.landmarkHistory.push([...landmarks]);
    if (this.landmarkHistory.length > this.maxHistorySize) {
      this.landmarkHistory.shift();
    }

    // 如果历史记录不足，直接返回
    if (this.landmarkHistory.length < 2) {
      return landmarks;
    }

    // 应用平滑滤波
    const smoothedLandmarks: LandmarkData[] = [];
    for (let i = 0; i < landmarks.length; i++) {
      const current = landmarks[i];
      const previous = this.landmarkHistory[this.landmarkHistory.length - 2][i];

      smoothedLandmarks.push({
        x: previous.x * this.smoothingFactor + current.x * (1 - this.smoothingFactor),
        y: previous.y * this.smoothingFactor + current.y * (1 - this.smoothingFactor),
        z: previous.z !== undefined && current.z !== undefined
          ? previous.z * this.smoothingFactor + current.z * (1 - this.smoothingFactor)
          : current.z,
        visibility: current.visibility
      });
    }

    return smoothedLandmarks;
  }

  /**
   * 重置滤波器
   */
  reset(): void {
    this.landmarkHistory = [];
  }

  /**
   * 设置平滑因子
   */
  setSmoothingFactor(factor: number): void {
    this.smoothingFactor = Math.max(0, Math.min(1, factor));
  }
}

/**
 * 真实的MediaPipe姿态检测器
 */
class RealMediaPipePoseDetector {
  private pose: any = null;
  private config: MediaPipeConfig;
  private isInitialized = false;
  private eventListeners: Map<string, Function[]> = new Map();
  private stabilityFilter: PoseStabilityFilter;
  private canvas: HTMLCanvasElement;
  private context: CanvasRenderingContext2D;

  constructor(config: MediaPipeConfig) {
    this.config = config;
    this.stabilityFilter = new PoseStabilityFilter();

    // 创建离屏画布用于处理
    this.canvas = document.createElement('canvas');
    this.context = this.canvas.getContext('2d')!;
  }

  /**
   * 初始化MediaPipe
   */
  async initialize(): Promise<void> {
    try {
      // 检查MediaPipe是否可用
      if (typeof window === 'undefined' || !(window as any).Pose) {
        throw new Error('MediaPipe Pose库未加载，请确保已引入MediaPipe脚本');
      }

      const Pose = (window as any).Pose;

      // 创建Pose实例
      this.pose = new Pose({
        locateFile: (file: string) => {
          return `https://cdn.jsdelivr.net/npm/@mediapipe/pose/${file}`;
        }
      });

      // 配置选项
      await this.pose.setOptions({
        modelComplexity: this.config.modelComplexity,
        smoothLandmarks: this.config.smoothLandmarks,
        enableSegmentation: this.config.enableSegmentation,
        smoothSegmentation: true,
        minDetectionConfidence: this.config.minDetectionConfidence,
        minTrackingConfidence: this.config.minTrackingConfidence,
        staticImageMode: this.config.staticImageMode
      });

      // 设置结果回调
      this.pose.onResults((results: any) => {
        this.handleResults(results);
      });

      this.isInitialized = true;
      this.emit('initialized');

      Debug.log('MediaPipePoseDetector', 'MediaPipe姿态检测器初始化成功');

    } catch (error) {
      Debug.error('MediaPipePoseDetector', 'MediaPipe初始化失败', error);
      this.isInitialized = false;
      this.emit('error', error);
      throw error;
    }
  }

  /**
   * 处理MediaPipe结果
   */
  private handleResults(results: any): void {
    const startTime = performance.now();

    try {
      const poseResults: PoseResults = {
        landmarks: [],
        worldLandmarks: [],
        segmentationMask: undefined,
        confidence: 0,
        processingTime: 0,
        timestamp: Date.now()
      };

      // 处理2D关键点
      if (results.poseLandmarks) {
        poseResults.landmarks = results.poseLandmarks.map((landmark: any) => ({
          x: landmark.x,
          y: landmark.y,
          z: landmark.z,
          visibility: landmark.visibility
        }));

        // 应用稳定性滤波
        if (this.config.smoothLandmarks) {
          poseResults.landmarks = this.stabilityFilter.applyFilter(poseResults.landmarks);
        }

        // 计算平均置信度
        poseResults.confidence = poseResults.landmarks.reduce((sum, landmark) =>
          sum + (landmark.visibility || 0), 0) / poseResults.landmarks.length;
      }

      // 处理3D世界坐标关键点
      if (results.poseWorldLandmarks && this.config.enableWorldLandmarks) {
        poseResults.worldLandmarks = results.poseWorldLandmarks.map((landmark: any) => ({
          x: landmark.x,
          y: landmark.y,
          z: landmark.z,
          visibility: landmark.visibility
        }));
      }

      // 处理分割掩码
      if (results.segmentationMask && this.config.enableSegmentation) {
        poseResults.segmentationMask = results.segmentationMask;
      }

      poseResults.processingTime = performance.now() - startTime;
      this.emit('results', poseResults);

    } catch (error) {
      Debug.error('MediaPipePoseDetector', '处理结果失败', error);
      this.emit('error', error);
    }
  }

  /**
   * 检测姿态
   */
  async detectPose(imageData: ImageData): Promise<PoseResults> {
    if (!this.isInitialized || !this.pose) {
      throw new Error('MediaPipe检测器未初始化');
    }

    try {
      // 将ImageData转换为HTMLImageElement
      this.canvas.width = imageData.width;
      this.canvas.height = imageData.height;
      this.context.putImageData(imageData, 0, 0);

      // 发送到MediaPipe进行处理
      await this.pose.send({ image: this.canvas });

      // 返回一个Promise，等待结果回调
      return new Promise((resolve, reject) => {
        const timeout = setTimeout(() => {
          reject(new Error('检测超时'));
        }, 5000);

        const onResults = (results: PoseResults) => {
          clearTimeout(timeout);
          this.off('results', onResults);
          this.off('error', onError);
          resolve(results);
        };

        const onError = (error: any) => {
          clearTimeout(timeout);
          this.off('results', onResults);
          this.off('error', onError);
          reject(error);
        };

        this.on('results', onResults);
        this.on('error', onError);
      });

    } catch (error) {
      Debug.error('MediaPipePoseDetector', '姿态检测失败', error);
      throw error;
    }
  }

  /**
   * 更新配置
   */
  async updateConfig(newConfig: Partial<MediaPipeConfig>): Promise<void> {
    this.config = { ...this.config, ...newConfig };

    if (this.pose && this.isInitialized) {
      try {
        await this.pose.setOptions({
          modelComplexity: this.config.modelComplexity,
          smoothLandmarks: this.config.smoothLandmarks,
          enableSegmentation: this.config.enableSegmentation,
          smoothSegmentation: true,
          minDetectionConfidence: this.config.minDetectionConfidence,
          minTrackingConfidence: this.config.minTrackingConfidence,
          staticImageMode: this.config.staticImageMode
        });

        // 更新稳定性滤波器
        if (newConfig.smoothLandmarks !== undefined) {
          if (!newConfig.smoothLandmarks) {
            this.stabilityFilter.reset();
          }
        }

        Debug.log('MediaPipePoseDetector', '配置更新成功');
      } catch (error) {
        Debug.error('MediaPipePoseDetector', '配置更新失败', error);
        throw error;
      }
    }
  }

  /**
   * 销毁检测器
   */
  destroy(): void {
    if (this.pose) {
      this.pose.close();
      this.pose = null;
    }

    this.isInitialized = false;
    this.stabilityFilter.reset();
    this.eventListeners.clear();

    Debug.log('MediaPipePoseDetector', '检测器已销毁');
  }

  /**
   * 事件监听
   */
  on(event: string, callback: Function): void {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, []);
    }
    this.eventListeners.get(event)!.push(callback);
  }

  /**
   * 移除事件监听
   */
  off(event: string, callback: Function): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      const index = listeners.indexOf(callback);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    }
  }

  /**
   * 发出事件
   */
  private emit(event: string, data?: any): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      listeners.forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          Debug.error('MediaPipePoseDetector', `事件回调执行失败: ${event}`, error);
        }
      });
    }
  }

  /**
   * 获取初始化状态
   */
  getInitialized(): boolean {
    return this.isInitialized;
  }

  /**
   * 检查MediaPipe是否可用
   */
  static isAvailable(): boolean {
    return typeof window !== 'undefined' && !!(window as any).Pose;
  }
}

/**
 * 姿态检测节点配置
 */
export interface PoseDetectionNodeConfig {
  /** 模型复杂度 (0-2) */
  modelComplexity: number;
  /** 是否启用平滑 */
  smoothLandmarks: boolean;
  /** 是否启用分割 */
  enableSegmentation: boolean;
  /** 最小检测置信度 */
  minDetectionConfidence: number;
  /** 最小跟踪置信度 */
  minTrackingConfidence: number;
  /** 是否启用世界坐标 */
  enableWorldLandmarks: boolean;
  /** 是否自动初始化 */
  autoInitialize: boolean;
  /** 是否启用静态图像模式 */
  staticImageMode: boolean;
  /** 置信度评估阈值 */
  confidenceThreshold: number;
}

/**
 * 姿态检测节点
 */
export class PoseDetectionNode extends VisualScriptNode {
  /** 节点类型 */
  public static readonly TYPE = 'PoseDetection';

  /** 节点名称 */
  public static readonly NAME = '姿态检测';

  /** 节点描述 */
  public static readonly DESCRIPTION = '使用MediaPipe检测人体姿态关键点，支持33个关键点检测';

  private poseDetector: RealMediaPipePoseDetector | null = null;
  private config: PoseDetectionNodeConfig;
  private isInitialized = false;
  private lastResults: PoseResults | null = null;
  private processingCount = 0;
  private successCount = 0;
  private averageProcessingTime = 0;
  private isProcessing = false;

  /** 默认配置 */
  private static readonly DEFAULT_CONFIG: PoseDetectionNodeConfig = {
    modelComplexity: 1,
    smoothLandmarks: true,
    enableSegmentation: false,
    minDetectionConfidence: 0.5,
    minTrackingConfidence: 0.5,
    enableWorldLandmarks: true,
    autoInitialize: true,
    staticImageMode: false,
    confidenceThreshold: 0.7
  };

  /** MediaPipe关键点索引映射 */
  private static readonly LANDMARK_INDICES = {
    NOSE: 0,
    LEFT_EYE_INNER: 1,
    LEFT_EYE: 2,
    LEFT_EYE_OUTER: 3,
    RIGHT_EYE_INNER: 4,
    RIGHT_EYE: 5,
    RIGHT_EYE_OUTER: 6,
    LEFT_EAR: 7,
    RIGHT_EAR: 8,
    MOUTH_LEFT: 9,
    MOUTH_RIGHT: 10,
    LEFT_SHOULDER: 11,
    RIGHT_SHOULDER: 12,
    LEFT_ELBOW: 13,
    RIGHT_ELBOW: 14,
    LEFT_WRIST: 15,
    RIGHT_WRIST: 16,
    LEFT_PINKY: 17,
    RIGHT_PINKY: 18,
    LEFT_INDEX: 19,
    RIGHT_INDEX: 20,
    LEFT_THUMB: 21,
    RIGHT_THUMB: 22,
    LEFT_HIP: 23,
    RIGHT_HIP: 24,
    LEFT_KNEE: 25,
    RIGHT_KNEE: 26,
    LEFT_ANKLE: 27,
    RIGHT_ANKLE: 28,
    LEFT_HEEL: 29,
    RIGHT_HEEL: 30,
    LEFT_FOOT_INDEX: 31,
    RIGHT_FOOT_INDEX: 32
  };

  constructor(nodeType: string = PoseDetectionNode.TYPE, name: string = PoseDetectionNode.NAME, id?: string) {
    super(nodeType, name, id);

    this.config = { ...PoseDetectionNode.DEFAULT_CONFIG };
    this.setupPorts();

    // 如果自动初始化，则立即初始化
    if (this.config.autoInitialize) {
      this.initializeDetector().catch(error => {
        Debug.error('PoseDetectionNode', '自动初始化失败', error);
      });
    }
  }

  /**
   * 设置输入输出端口
   */
  private setupPorts(): void {
    // 输入端口
    this.addInput('imageData', 'object', '图像数据');
    this.addInput('detect', 'trigger', '检测');
    this.addInput('initialize', 'trigger', '初始化');
    this.addInput('reset', 'trigger', '重置');
    this.addInput('modelComplexity', 'number', '模型复杂度');
    this.addInput('minDetectionConfidence', 'number', '检测置信度');
    this.addInput('minTrackingConfidence', 'number', '跟踪置信度');
    this.addInput('enableSegmentation', 'boolean', '启用分割');
    this.addInput('smoothLandmarks', 'boolean', '启用平滑');

    // 输出端口
    this.addOutput('landmarks', 'array', '2D关键点');
    this.addOutput('worldLandmarks', 'array', '3D关键点');
    this.addOutput('segmentationMask', 'object', '分割掩码');
    this.addOutput('confidence', 'number', '置信度');
    this.addOutput('isDetected', 'boolean', '检测成功');
    this.addOutput('processingTime', 'number', '处理时间');
    this.addOutput('averageProcessingTime', 'number', '平均处理时间');
    this.addOutput('successRate', 'number', '成功率');
    this.addOutput('isProcessing', 'boolean', '正在处理');
    this.addOutput('isInitialized', 'boolean', '已初始化');
    this.addOutput('onDetected', 'trigger', '检测完成');
    this.addOutput('onInitialized', 'trigger', '初始化完成');
    this.addOutput('onError', 'trigger', '错误');

    // 特定关键点输出
    this.addOutput('nose', 'object', '鼻子');
    this.addOutput('leftEye', 'object', '左眼');
    this.addOutput('rightEye', 'object', '右眼');
    this.addOutput('leftEar', 'object', '左耳');
    this.addOutput('rightEar', 'object', '右耳');
    this.addOutput('leftShoulder', 'object', '左肩');
    this.addOutput('rightShoulder', 'object', '右肩');
    this.addOutput('leftElbow', 'object', '左肘');
    this.addOutput('rightElbow', 'object', '右肘');
    this.addOutput('leftWrist', 'object', '左腕');
    this.addOutput('rightWrist', 'object', '右腕');
    this.addOutput('leftHip', 'object', '左髋');
    this.addOutput('rightHip', 'object', '右髋');
    this.addOutput('leftKnee', 'object', '左膝');
    this.addOutput('rightKnee', 'object', '右膝');
    this.addOutput('leftAnkle', 'object', '左踝');
    this.addOutput('rightAnkle', 'object', '右踝');

    // 身体部位组合输出
    this.addOutput('head', 'object', '头部关键点');
    this.addOutput('torso', 'object', '躯干关键点');
    this.addOutput('leftArm', 'object', '左臂关键点');
    this.addOutput('rightArm', 'object', '右臂关键点');
    this.addOutput('leftLeg', 'object', '左腿关键点');
    this.addOutput('rightLeg', 'object', '右腿关键点');
  }

  /**
   * 执行节点
   */
  public async execute(inputs?: any): Promise<any> {
    try {
      // 检查输入
      const imageData = inputs?.imageData as ImageData;
      const detectTrigger = inputs?.detect;
      const initializeTrigger = inputs?.initialize;
      const resetTrigger = inputs?.reset;
      const modelComplexity = inputs?.modelComplexity as number;
      const minDetectionConfidence = inputs?.minDetectionConfidence as number;
      const minTrackingConfidence = inputs?.minTrackingConfidence as number;
      const enableSegmentation = inputs?.enableSegmentation as boolean;
      const smoothLandmarks = inputs?.smoothLandmarks as boolean;

      // 更新配置
      let configChanged = false;
      if (modelComplexity !== undefined && modelComplexity !== this.config.modelComplexity) {
        this.config.modelComplexity = Math.max(0, Math.min(2, Math.floor(modelComplexity)));
        configChanged = true;
      }
      if (minDetectionConfidence !== undefined && minDetectionConfidence !== this.config.minDetectionConfidence) {
        this.config.minDetectionConfidence = Math.max(0, Math.min(1, minDetectionConfidence));
        configChanged = true;
      }
      if (minTrackingConfidence !== undefined && minTrackingConfidence !== this.config.minTrackingConfidence) {
        this.config.minTrackingConfidence = Math.max(0, Math.min(1, minTrackingConfidence));
        configChanged = true;
      }
      if (enableSegmentation !== undefined && enableSegmentation !== this.config.enableSegmentation) {
        this.config.enableSegmentation = enableSegmentation;
        configChanged = true;
      }
      if (smoothLandmarks !== undefined && smoothLandmarks !== this.config.smoothLandmarks) {
        this.config.smoothLandmarks = smoothLandmarks;
        configChanged = true;
      }

      // 如果配置改变且检测器已初始化，更新配置
      if (configChanged && this.isInitialized && this.poseDetector) {
        await this.poseDetector.updateConfig(this.config);
      }

      // 处理重置触发
      if (resetTrigger) {
        await this.resetDetector();
      }

      // 处理初始化触发
      if (initializeTrigger || (!this.isInitialized && this.config.autoInitialize)) {
        await this.initializeDetector();
      }

      // 处理检测触发
      if (detectTrigger && imageData && this.isInitialized && !this.isProcessing) {
        await this.detectPose(imageData);
      }

      // 返回输出
      return this.getNodeOutputs();

    } catch (error) {
      Debug.error('PoseDetectionNode', '节点执行失败', String(error));
      return {
        onError: true,
        isInitialized: this.isInitialized,
        isProcessing: this.isProcessing
      };
    }
  }

  /**
   * 获取节点输出值
   */
  private getNodeOutputs(): any {
    const baseOutputs = {
      landmarks: [],
      worldLandmarks: [],
      segmentationMask: null,
      confidence: 0,
      isDetected: false,
      processingTime: 0,
      averageProcessingTime: this.averageProcessingTime,
      successRate: this.processingCount > 0 ? this.successCount / this.processingCount : 0,
      isProcessing: this.isProcessing,
      isInitialized: this.isInitialized,
      onDetected: false,
      onInitialized: false,
      onError: false
    };

    if (!this.lastResults) {
      return baseOutputs;
    }

    const results = this.lastResults;

    // 提取特定关键点
    const specificLandmarks: any = {};
    const bodyParts: any = {};

    if (results.landmarks && results.landmarks.length >= 33) {
      const landmarks = results.landmarks;
      const indices = PoseDetectionNode.LANDMARK_INDICES;

      // 特定关键点
      specificLandmarks.nose = landmarks[indices.NOSE];
      specificLandmarks.leftEye = landmarks[indices.LEFT_EYE];
      specificLandmarks.rightEye = landmarks[indices.RIGHT_EYE];
      specificLandmarks.leftEar = landmarks[indices.LEFT_EAR];
      specificLandmarks.rightEar = landmarks[indices.RIGHT_EAR];
      specificLandmarks.leftShoulder = landmarks[indices.LEFT_SHOULDER];
      specificLandmarks.rightShoulder = landmarks[indices.RIGHT_SHOULDER];
      specificLandmarks.leftElbow = landmarks[indices.LEFT_ELBOW];
      specificLandmarks.rightElbow = landmarks[indices.RIGHT_ELBOW];
      specificLandmarks.leftWrist = landmarks[indices.LEFT_WRIST];
      specificLandmarks.rightWrist = landmarks[indices.RIGHT_WRIST];
      specificLandmarks.leftHip = landmarks[indices.LEFT_HIP];
      specificLandmarks.rightHip = landmarks[indices.RIGHT_HIP];
      specificLandmarks.leftKnee = landmarks[indices.LEFT_KNEE];
      specificLandmarks.rightKnee = landmarks[indices.RIGHT_KNEE];
      specificLandmarks.leftAnkle = landmarks[indices.LEFT_ANKLE];
      specificLandmarks.rightAnkle = landmarks[indices.RIGHT_ANKLE];

      // 身体部位组合
      bodyParts.head = [
        landmarks[indices.NOSE],
        landmarks[indices.LEFT_EYE],
        landmarks[indices.RIGHT_EYE],
        landmarks[indices.LEFT_EAR],
        landmarks[indices.RIGHT_EAR]
      ];

      bodyParts.torso = [
        landmarks[indices.LEFT_SHOULDER],
        landmarks[indices.RIGHT_SHOULDER],
        landmarks[indices.LEFT_HIP],
        landmarks[indices.RIGHT_HIP]
      ];

      bodyParts.leftArm = [
        landmarks[indices.LEFT_SHOULDER],
        landmarks[indices.LEFT_ELBOW],
        landmarks[indices.LEFT_WRIST]
      ];

      bodyParts.rightArm = [
        landmarks[indices.RIGHT_SHOULDER],
        landmarks[indices.RIGHT_ELBOW],
        landmarks[indices.RIGHT_WRIST]
      ];

      bodyParts.leftLeg = [
        landmarks[indices.LEFT_HIP],
        landmarks[indices.LEFT_KNEE],
        landmarks[indices.LEFT_ANKLE]
      ];

      bodyParts.rightLeg = [
        landmarks[indices.RIGHT_HIP],
        landmarks[indices.RIGHT_KNEE],
        landmarks[indices.RIGHT_ANKLE]
      ];
    }

    return {
      landmarks: results.landmarks || [],
      worldLandmarks: results.worldLandmarks || [],
      segmentationMask: results.segmentationMask || null,
      confidence: results.confidence,
      isDetected: results.confidence > this.config.confidenceThreshold,
      processingTime: results.processingTime,
      averageProcessingTime: this.averageProcessingTime,
      successRate: this.processingCount > 0 ? this.successCount / this.processingCount : 0,
      isProcessing: this.isProcessing,
      isInitialized: this.isInitialized,
      onDetected: false,
      onInitialized: false,
      onError: false,
      ...specificLandmarks,
      ...bodyParts
    };
  }

  /**
   * 初始化检测器
   */
  private async initializeDetector(): Promise<void> {
    try {
      if (this.isInitialized) {
        Debug.log('PoseDetectionNode', '检测器已初始化');
        return;
      }

      // 检查MediaPipe是否可用
      if (!RealMediaPipePoseDetector.isAvailable()) {
        throw new Error('MediaPipe库未加载，请确保已引入MediaPipe脚本');
      }

      // 创建检测器
      this.poseDetector = new RealMediaPipePoseDetector(this.config);

      // 设置事件监听
      this.setupDetectorEvents();

      // 初始化
      await this.poseDetector.initialize();

      this.isInitialized = true;
      this.processingCount = 0;
      this.successCount = 0;
      this.averageProcessingTime = 0;

      Debug.log('PoseDetectionNode', '姿态检测器初始化成功');

    } catch (error) {
      Debug.error('PoseDetectionNode', '初始化检测器失败', String(error));
      this.isInitialized = false;
      throw error;
    }
  }

  /**
   * 重置检测器
   */
  private async resetDetector(): Promise<void> {
    try {
      if (this.poseDetector) {
        this.poseDetector.destroy();
        this.poseDetector = null;
      }

      this.isInitialized = false;
      this.lastResults = null;
      this.processingCount = 0;
      this.successCount = 0;
      this.averageProcessingTime = 0;
      this.isProcessing = false;

      Debug.log('PoseDetectionNode', '检测器已重置');

    } catch (error) {
      Debug.error('PoseDetectionNode', '重置检测器失败', String(error));
      throw error;
    }
  }

  /**
   * 设置检测器事件监听
   */
  private setupDetectorEvents(): void {
    if (!this.poseDetector) return;

    this.poseDetector.on('results', (results: PoseResults) => {
      this.lastResults = results;
      this.successCount++;

      // 更新平均处理时间
      this.averageProcessingTime = (this.averageProcessingTime * (this.processingCount - 1) + results.processingTime) / this.processingCount;

      this.isProcessing = false;
      Debug.log('PoseDetectionNode', `姿态检测完成，置信度: ${results.confidence.toFixed(3)}`);
    });

    this.poseDetector.on('error', (error: any) => {
      this.isProcessing = false;
      Debug.error('PoseDetectionNode', '检测器错误', error);
    });

    this.poseDetector.on('initialized', () => {
      Debug.log('PoseDetectionNode', '检测器初始化事件');
    });
  }

  /**
   * 检测姿态
   */
  private async detectPose(imageData: ImageData): Promise<void> {
    if (!this.isInitialized || !this.poseDetector) {
      Debug.error('PoseDetectionNode', '检测器未初始化');
      return;
    }

    if (this.isProcessing) {
      Debug.warn('PoseDetectionNode', '检测正在进行中，跳过此次检测');
      return;
    }

    try {
      this.isProcessing = true;
      this.processingCount++;

      // 执行检测
      const results = await this.poseDetector.detectPose(imageData);

      // 结果已在事件回调中处理
      Debug.log('PoseDetectionNode', '姿态检测完成');

    } catch (error) {
      this.isProcessing = false;
      Debug.error('PoseDetectionNode', '姿态检测失败', String(error));
      throw error;
    }
  }

  /**
   * 获取节点配置
   */
  public getConfig(): PoseDetectionNodeConfig {
    return { ...this.config };
  }

  /**
   * 更新节点配置
   */
  public async updateConfig(newConfig: Partial<PoseDetectionNodeConfig>): Promise<void> {
    const oldConfig = { ...this.config };
    this.config = { ...this.config, ...newConfig };

    // 如果检测器已初始化，更新其配置
    if (this.poseDetector && this.isInitialized) {
      try {
        await this.poseDetector.updateConfig(this.config);
        Debug.log('PoseDetectionNode', '配置更新成功', { oldConfig, newConfig: this.config });
      } catch (error) {
        Debug.error('PoseDetectionNode', '配置更新失败', error);
        throw error;
      }
    }
  }

  /**
   * 获取最后检测结果
   */
  public getLastResults(): PoseResults | null {
    return this.lastResults ? { ...this.lastResults } : null;
  }

  /**
   * 获取检测成功率
   */
  public getSuccessRate(): number {
    return this.processingCount > 0 ? this.successCount / this.processingCount : 0;
  }

  /**
   * 获取平均处理时间
   */
  public getAverageProcessingTime(): number {
    return this.averageProcessingTime;
  }

  /**
   * 获取处理统计
   */
  public getProcessingStats(): {
    totalProcessed: number;
    successCount: number;
    successRate: number;
    averageProcessingTime: number;
  } {
    return {
      totalProcessed: this.processingCount,
      successCount: this.successCount,
      successRate: this.getSuccessRate(),
      averageProcessingTime: this.averageProcessingTime
    };
  }

  /**
   * 是否已初始化
   */
  public get initialized(): boolean {
    return this.isInitialized;
  }

  /**
   * 是否正在处理
   */
  public get processing(): boolean {
    return this.isProcessing;
  }

  /**
   * 检查MediaPipe是否可用
   */
  public static isMediaPipeAvailable(): boolean {
    return RealMediaPipePoseDetector.isAvailable();
  }

  /**
   * 获取关键点名称映射
   */
  public static getLandmarkNames(): Record<string, number> {
    return { ...PoseDetectionNode.LANDMARK_INDICES };
  }

  /**
   * 计算关键点之间的距离
   */
  public static calculateDistance(point1: LandmarkData, point2: LandmarkData): number {
    const dx = point1.x - point2.x;
    const dy = point1.y - point2.y;
    const dz = (point1.z || 0) - (point2.z || 0);
    return Math.sqrt(dx * dx + dy * dy + dz * dz);
  }

  /**
   * 计算关键点的角度
   */
  public static calculateAngle(point1: LandmarkData, vertex: LandmarkData, point2: LandmarkData): number {
    const v1 = { x: point1.x - vertex.x, y: point1.y - vertex.y };
    const v2 = { x: point2.x - vertex.x, y: point2.y - vertex.y };

    const dot = v1.x * v2.x + v1.y * v2.y;
    const mag1 = Math.sqrt(v1.x * v1.x + v1.y * v1.y);
    const mag2 = Math.sqrt(v2.x * v2.x + v2.y * v2.y);

    if (mag1 === 0 || mag2 === 0) return 0;

    const cosAngle = dot / (mag1 * mag2);
    return Math.acos(Math.max(-1, Math.min(1, cosAngle))) * (180 / Math.PI);
  }

  /**
   * 销毁节点
   */
  public destroy(): void {
    if (this.poseDetector) {
      this.poseDetector.destroy();
      this.poseDetector = null;
    }

    this.isInitialized = false;
    this.isProcessing = false;
    this.lastResults = null;
    this.processingCount = 0;
    this.successCount = 0;
    this.averageProcessingTime = 0;

    super.destroy?.();
  }
}
