/**
 * 身体分析节点
 * 基于姿态关键点进行身体姿势分析和动作识别
 */
import { VisualScriptNode } from '../../../visualscript/VisualScriptNode';
import { Debug } from '../../../utils/Debug';
import { Vector3, Vector2 } from 'three';

/**
 * 身体关键点数据接口
 */
export interface BodyLandmarkData {
  x: number;
  y: number;
  z?: number;
  visibility?: number;
}

/**
 * 身体姿势类型枚举
 */
export enum BodyPosture {
  STANDING = 'standing',
  SITTING = 'sitting',
  LYING = 'lying',
  CROUCHING = 'crouching',
  KNEELING = 'kneeling',
  LEANING_FORWARD = 'leaning_forward',
  LEANING_BACKWARD = 'leaning_backward',
  LEANING_LEFT = 'leaning_left',
  LEANING_RIGHT = 'leaning_right'
}

/**
 * 动作类型枚举
 */
export enum ActionType {
  WALKING = 'walking',
  RUNNING = 'running',
  JUMPING = 'jumping',
  WAVING = 'waving',
  CLAPPING = 'clapping',
  STRETCHING = 'stretching',
  DANCING = 'dancing',
  EXERCISING = 'exercising',
  STATIC = 'static'
}

/**
 * 身体部位角度数据
 */
export interface BodyAngles {
  leftShoulder: number;
  rightShoulder: number;
  leftElbow: number;
  rightElbow: number;
  leftHip: number;
  rightHip: number;
  leftKnee: number;
  rightKnee: number;
  spineAngle: number;
  neckAngle: number;
}

/**
 * 身体对称性分析
 */
export interface BodySymmetry {
  shoulderSymmetry: number;
  hipSymmetry: number;
  armSymmetry: number;
  legSymmetry: number;
  overallSymmetry: number;
}

/**
 * 身体分析结果
 */
export interface BodyAnalysisResults {
  posture: BodyPosture;
  action: ActionType;
  bodyAngles: BodyAngles;
  bodySymmetry: BodySymmetry;
  balance: number;
  stability: number;
  energy: number;
  confidence: number;
  processingTime: number;
  timestamp: number;
}

/**
 * 高级身体分析器
 */
class AdvancedBodyAnalyzer {
  private postureHistory: BodyPosture[] = [];
  private actionHistory: ActionType[] = [];
  private angleHistory: BodyAngles[] = [];
  private maxHistorySize = 30; // 1秒历史（假设30fps）
  private stabilityThreshold = 0.1;

  /**
   * MediaPipe姿态关键点索引
   */
  private static readonly POSE_LANDMARKS = {
    NOSE: 0,
    LEFT_EYE_INNER: 1,
    LEFT_EYE: 2,
    LEFT_EYE_OUTER: 3,
    RIGHT_EYE_INNER: 4,
    RIGHT_EYE: 5,
    RIGHT_EYE_OUTER: 6,
    LEFT_EAR: 7,
    RIGHT_EAR: 8,
    MOUTH_LEFT: 9,
    MOUTH_RIGHT: 10,
    LEFT_SHOULDER: 11,
    RIGHT_SHOULDER: 12,
    LEFT_ELBOW: 13,
    RIGHT_ELBOW: 14,
    LEFT_WRIST: 15,
    RIGHT_WRIST: 16,
    LEFT_PINKY: 17,
    RIGHT_PINKY: 18,
    LEFT_INDEX: 19,
    RIGHT_INDEX: 20,
    LEFT_THUMB: 21,
    RIGHT_THUMB: 22,
    LEFT_HIP: 23,
    RIGHT_HIP: 24,
    LEFT_KNEE: 25,
    RIGHT_KNEE: 26,
    LEFT_ANKLE: 27,
    RIGHT_ANKLE: 28,
    LEFT_HEEL: 29,
    RIGHT_HEEL: 30,
    LEFT_FOOT_INDEX: 31,
    RIGHT_FOOT_INDEX: 32
  };

  /**
   * 分析身体姿态
   */
  analyzeBody(poseLandmarks: BodyLandmarkData[]): BodyAnalysisResults {
    if (poseLandmarks.length < 33) {
      throw new Error('姿态关键点数据不足，需要至少33个关键点');
    }

    // 分析身体姿势
    const posture = this.analyzePosture(poseLandmarks);
    
    // 分析动作类型
    const action = this.analyzeAction(poseLandmarks);
    
    // 计算身体角度
    const bodyAngles = this.calculateBodyAngles(poseLandmarks);
    
    // 分析身体对称性
    const bodySymmetry = this.analyzeBodySymmetry(poseLandmarks);
    
    // 计算平衡性
    const balance = this.calculateBalance(poseLandmarks);
    
    // 计算稳定性
    const stability = this.calculateStability(poseLandmarks);
    
    // 计算能量水平
    const energy = this.calculateEnergyLevel(poseLandmarks);
    
    // 计算置信度
    const confidence = this.calculateConfidence(poseLandmarks);

    // 更新历史记录
    this.updateHistory(posture, action, bodyAngles);

    return {
      posture,
      action,
      bodyAngles,
      bodySymmetry,
      balance,
      stability,
      energy,
      confidence,
      processingTime: 0,
      timestamp: Date.now()
    };
  }

  /**
   * 分析身体姿势
   */
  private analyzePosture(landmarks: BodyLandmarkData[]): BodyPosture {
    const indices = AdvancedBodyAnalyzer.POSE_LANDMARKS;
    
    // 获取关键点
    const nose = landmarks[indices.NOSE];
    const leftShoulder = landmarks[indices.LEFT_SHOULDER];
    const rightShoulder = landmarks[indices.RIGHT_SHOULDER];
    const leftHip = landmarks[indices.LEFT_HIP];
    const rightHip = landmarks[indices.RIGHT_HIP];
    const leftKnee = landmarks[indices.LEFT_KNEE];
    const rightKnee = landmarks[indices.RIGHT_KNEE];
    const leftAnkle = landmarks[indices.LEFT_ANKLE];
    const rightAnkle = landmarks[indices.RIGHT_ANKLE];

    // 计算身体中心线
    const shoulderCenter = { x: (leftShoulder.x + rightShoulder.x) / 2, y: (leftShoulder.y + rightShoulder.y) / 2 };
    const hipCenter = { x: (leftHip.x + rightHip.x) / 2, y: (leftHip.y + rightHip.y) / 2 };
    const kneeCenter = { x: (leftKnee.x + rightKnee.x) / 2, y: (leftKnee.y + rightKnee.y) / 2 };
    const ankleCenter = { x: (leftAnkle.x + rightAnkle.x) / 2, y: (leftAnkle.y + rightAnkle.y) / 2 };

    // 计算身体倾斜角度
    const spineAngle = Math.atan2(shoulderCenter.x - hipCenter.x, shoulderCenter.y - hipCenter.y) * 180 / Math.PI;
    const legAngle = Math.atan2(hipCenter.x - kneeCenter.x, hipCenter.y - kneeCenter.y) * 180 / Math.PI;

    // 计算膝盖弯曲程度
    const leftKneeBend = this.calculateAngle(leftHip, leftKnee, leftAnkle);
    const rightKneeBend = this.calculateAngle(rightHip, rightKnee, rightAnkle);
    const avgKneeBend = (leftKneeBend + rightKneeBend) / 2;

    // 判断姿势
    if (avgKneeBend < 120) {
      if (hipCenter.y > kneeCenter.y + 0.1) {
        return BodyPosture.SITTING;
      } else {
        return BodyPosture.CROUCHING;
      }
    } else if (Math.abs(spineAngle) > 20) {
      if (spineAngle > 20) {
        return BodyPosture.LEANING_RIGHT;
      } else {
        return BodyPosture.LEANING_LEFT;
      }
    } else if (shoulderCenter.y > hipCenter.y + 0.2) {
      return BodyPosture.LEANING_FORWARD;
    } else if (shoulderCenter.y < hipCenter.y - 0.1) {
      return BodyPosture.LEANING_BACKWARD;
    } else {
      return BodyPosture.STANDING;
    }
  }

  /**
   * 分析动作类型
   */
  private analyzeAction(landmarks: BodyLandmarkData[]): ActionType {
    // 计算关键点的运动幅度
    const motionMagnitude = this.calculateMotionMagnitude(landmarks);
    
    // 分析手臂运动
    const armMotion = this.analyzeArmMotion(landmarks);
    
    // 分析腿部运动
    const legMotion = this.analyzeLegMotion(landmarks);

    // 基于运动模式判断动作
    if (motionMagnitude < 0.01) {
      return ActionType.STATIC;
    } else if (armMotion.isWaving) {
      return ActionType.WAVING;
    } else if (armMotion.isClapping) {
      return ActionType.CLAPPING;
    } else if (legMotion.isJumping) {
      return ActionType.JUMPING;
    } else if (legMotion.isRunning) {
      return ActionType.RUNNING;
    } else if (legMotion.isWalking) {
      return ActionType.WALKING;
    } else if (motionMagnitude > 0.1) {
      return ActionType.DANCING;
    } else {
      return ActionType.STRETCHING;
    }
  }

  /**
   * 计算身体角度
   */
  private calculateBodyAngles(landmarks: BodyLandmarkData[]): BodyAngles {
    const indices = AdvancedBodyAnalyzer.POSE_LANDMARKS;

    return {
      leftShoulder: this.calculateAngle(
        landmarks[indices.LEFT_ELBOW],
        landmarks[indices.LEFT_SHOULDER],
        landmarks[indices.LEFT_HIP]
      ),
      rightShoulder: this.calculateAngle(
        landmarks[indices.RIGHT_ELBOW],
        landmarks[indices.RIGHT_SHOULDER],
        landmarks[indices.RIGHT_HIP]
      ),
      leftElbow: this.calculateAngle(
        landmarks[indices.LEFT_SHOULDER],
        landmarks[indices.LEFT_ELBOW],
        landmarks[indices.LEFT_WRIST]
      ),
      rightElbow: this.calculateAngle(
        landmarks[indices.RIGHT_SHOULDER],
        landmarks[indices.RIGHT_ELBOW],
        landmarks[indices.RIGHT_WRIST]
      ),
      leftHip: this.calculateAngle(
        landmarks[indices.LEFT_SHOULDER],
        landmarks[indices.LEFT_HIP],
        landmarks[indices.LEFT_KNEE]
      ),
      rightHip: this.calculateAngle(
        landmarks[indices.RIGHT_SHOULDER],
        landmarks[indices.RIGHT_HIP],
        landmarks[indices.RIGHT_KNEE]
      ),
      leftKnee: this.calculateAngle(
        landmarks[indices.LEFT_HIP],
        landmarks[indices.LEFT_KNEE],
        landmarks[indices.LEFT_ANKLE]
      ),
      rightKnee: this.calculateAngle(
        landmarks[indices.RIGHT_HIP],
        landmarks[indices.RIGHT_KNEE],
        landmarks[indices.RIGHT_ANKLE]
      ),
      spineAngle: this.calculateSpineAngle(landmarks),
      neckAngle: this.calculateNeckAngle(landmarks)
    };
  }

  /**
   * 分析身体对称性
   */
  private analyzeBodySymmetry(landmarks: BodyLandmarkData[]): BodySymmetry {
    const angles = this.calculateBodyAngles(landmarks);

    const shoulderSymmetry = 1 - Math.abs(angles.leftShoulder - angles.rightShoulder) / 180;
    const hipSymmetry = 1 - Math.abs(angles.leftHip - angles.rightHip) / 180;
    const armSymmetry = 1 - Math.abs(angles.leftElbow - angles.rightElbow) / 180;
    const legSymmetry = 1 - Math.abs(angles.leftKnee - angles.rightKnee) / 180;

    const overallSymmetry = (shoulderSymmetry + hipSymmetry + armSymmetry + legSymmetry) / 4;

    return {
      shoulderSymmetry: Math.max(0, shoulderSymmetry),
      hipSymmetry: Math.max(0, hipSymmetry),
      armSymmetry: Math.max(0, armSymmetry),
      legSymmetry: Math.max(0, legSymmetry),
      overallSymmetry: Math.max(0, overallSymmetry)
    };
  }

  /**
   * 计算平衡性
   */
  private calculateBalance(landmarks: BodyLandmarkData[]): number {
    const indices = AdvancedBodyAnalyzer.POSE_LANDMARKS;
    
    // 计算重心
    const centerOfMass = this.calculateCenterOfMass(landmarks);
    
    // 计算支撑基底（脚部位置）
    const leftFoot = landmarks[indices.LEFT_ANKLE];
    const rightFoot = landmarks[indices.RIGHT_ANKLE];
    const supportBase = { x: (leftFoot.x + rightFoot.x) / 2, y: (leftFoot.y + rightFoot.y) / 2 };
    
    // 计算重心偏离支撑基底的距离
    const deviation = Math.sqrt(
      Math.pow(centerOfMass.x - supportBase.x, 2) + 
      Math.pow(centerOfMass.y - supportBase.y, 2)
    );
    
    // 平衡性 = 1 - 偏离程度
    return Math.max(0, 1 - deviation * 5); // 调整系数
  }

  /**
   * 计算稳定性
   */
  private calculateStability(landmarks: BodyLandmarkData[]): number {
    if (this.angleHistory.length < 5) return 0.5;

    // 计算最近几帧的角度变化
    let totalVariation = 0;
    const recentHistory = this.angleHistory.slice(-5);
    
    for (let i = 1; i < recentHistory.length; i++) {
      const current = recentHistory[i];
      const previous = recentHistory[i - 1];
      
      const variation = Math.abs(current.spineAngle - previous.spineAngle) +
                       Math.abs(current.leftShoulder - previous.leftShoulder) +
                       Math.abs(current.rightShoulder - previous.rightShoulder);
      
      totalVariation += variation;
    }

    // 稳定性 = 1 - 变化程度
    const stability = Math.max(0, 1 - totalVariation / 100); // 调整系数
    return stability;
  }

  /**
   * 计算能量水平
   */
  private calculateEnergyLevel(landmarks: BodyLandmarkData[]): number {
    const motionMagnitude = this.calculateMotionMagnitude(landmarks);
    return Math.min(1, motionMagnitude * 10); // 调整系数
  }

  /**
   * 计算置信度
   */
  private calculateConfidence(landmarks: BodyLandmarkData[]): number {
    // 基于关键点可见性计算置信度
    let totalVisibility = 0;
    let visibleCount = 0;

    for (const landmark of landmarks) {
      if (landmark.visibility !== undefined) {
        totalVisibility += landmark.visibility;
        visibleCount++;
      }
    }

    return visibleCount > 0 ? totalVisibility / visibleCount : 0;
  }

  /**
   * 计算角度
   */
  private calculateAngle(point1: BodyLandmarkData, vertex: BodyLandmarkData, point2: BodyLandmarkData): number {
    const v1 = { x: point1.x - vertex.x, y: point1.y - vertex.y };
    const v2 = { x: point2.x - vertex.x, y: point2.y - vertex.y };
    
    const dot = v1.x * v2.x + v1.y * v2.y;
    const mag1 = Math.sqrt(v1.x * v1.x + v1.y * v1.y);
    const mag2 = Math.sqrt(v2.x * v2.x + v2.y * v2.y);
    
    if (mag1 === 0 || mag2 === 0) return 0;
    
    const cosAngle = dot / (mag1 * mag2);
    return Math.acos(Math.max(-1, Math.min(1, cosAngle))) * (180 / Math.PI);
  }

  /**
   * 计算脊柱角度
   */
  private calculateSpineAngle(landmarks: BodyLandmarkData[]): number {
    const indices = AdvancedBodyAnalyzer.POSE_LANDMARKS;
    const nose = landmarks[indices.NOSE];
    const leftShoulder = landmarks[indices.LEFT_SHOULDER];
    const rightShoulder = landmarks[indices.RIGHT_SHOULDER];
    const leftHip = landmarks[indices.LEFT_HIP];
    const rightHip = landmarks[indices.RIGHT_HIP];

    const shoulderCenter = { x: (leftShoulder.x + rightShoulder.x) / 2, y: (leftShoulder.y + rightShoulder.y) / 2 };
    const hipCenter = { x: (leftHip.x + rightHip.x) / 2, y: (leftHip.y + rightHip.y) / 2 };

    return Math.atan2(shoulderCenter.x - hipCenter.x, shoulderCenter.y - hipCenter.y) * 180 / Math.PI;
  }

  /**
   * 计算颈部角度
   */
  private calculateNeckAngle(landmarks: BodyLandmarkData[]): number {
    const indices = AdvancedBodyAnalyzer.POSE_LANDMARKS;
    const nose = landmarks[indices.NOSE];
    const leftShoulder = landmarks[indices.LEFT_SHOULDER];
    const rightShoulder = landmarks[indices.RIGHT_SHOULDER];

    const shoulderCenter = { x: (leftShoulder.x + rightShoulder.x) / 2, y: (leftShoulder.y + rightShoulder.y) / 2 };

    return Math.atan2(nose.x - shoulderCenter.x, nose.y - shoulderCenter.y) * 180 / Math.PI;
  }

  /**
   * 计算运动幅度
   */
  private calculateMotionMagnitude(landmarks: BodyLandmarkData[]): number {
    if (this.angleHistory.length < 2) return 0;

    const current = this.calculateBodyAngles(landmarks);
    const previous = this.angleHistory[this.angleHistory.length - 1];

    const totalChange = Math.abs(current.leftShoulder - previous.leftShoulder) +
                       Math.abs(current.rightShoulder - previous.rightShoulder) +
                       Math.abs(current.leftElbow - previous.leftElbow) +
                       Math.abs(current.rightElbow - previous.rightElbow) +
                       Math.abs(current.leftHip - previous.leftHip) +
                       Math.abs(current.rightHip - previous.rightHip) +
                       Math.abs(current.leftKnee - previous.leftKnee) +
                       Math.abs(current.rightKnee - previous.rightKnee);

    return totalChange / 8; // 平均变化
  }

  /**
   * 分析手臂运动
   */
  private analyzeArmMotion(landmarks: BodyLandmarkData[]): { isWaving: boolean; isClapping: boolean } {
    // 简化的手臂运动分析
    const motionMagnitude = this.calculateMotionMagnitude(landmarks);
    
    return {
      isWaving: motionMagnitude > 0.05 && motionMagnitude < 0.2,
      isClapping: motionMagnitude > 0.1 && motionMagnitude < 0.3
    };
  }

  /**
   * 分析腿部运动
   */
  private analyzeLegMotion(landmarks: BodyLandmarkData[]): { isWalking: boolean; isRunning: boolean; isJumping: boolean } {
    // 简化的腿部运动分析
    const motionMagnitude = this.calculateMotionMagnitude(landmarks);
    
    return {
      isWalking: motionMagnitude > 0.02 && motionMagnitude < 0.1,
      isRunning: motionMagnitude > 0.1 && motionMagnitude < 0.3,
      isJumping: motionMagnitude > 0.3
    };
  }

  /**
   * 计算重心
   */
  private calculateCenterOfMass(landmarks: BodyLandmarkData[]): { x: number; y: number } {
    const indices = AdvancedBodyAnalyzer.POSE_LANDMARKS;
    
    // 主要身体部位的权重
    const keyPoints = [
      { point: landmarks[indices.NOSE], weight: 0.1 },
      { point: landmarks[indices.LEFT_SHOULDER], weight: 0.15 },
      { point: landmarks[indices.RIGHT_SHOULDER], weight: 0.15 },
      { point: landmarks[indices.LEFT_HIP], weight: 0.2 },
      { point: landmarks[indices.RIGHT_HIP], weight: 0.2 },
      { point: landmarks[indices.LEFT_KNEE], weight: 0.1 },
      { point: landmarks[indices.RIGHT_KNEE], weight: 0.1 }
    ];

    let totalX = 0, totalY = 0, totalWeight = 0;
    
    for (const { point, weight } of keyPoints) {
      if (point) {
        totalX += point.x * weight;
        totalY += point.y * weight;
        totalWeight += weight;
      }
    }

    return {
      x: totalWeight > 0 ? totalX / totalWeight : 0,
      y: totalWeight > 0 ? totalY / totalWeight : 0
    };
  }

  /**
   * 更新历史记录
   */
  private updateHistory(posture: BodyPosture, action: ActionType, angles: BodyAngles): void {
    this.postureHistory.push(posture);
    this.actionHistory.push(action);
    this.angleHistory.push(angles);

    if (this.postureHistory.length > this.maxHistorySize) {
      this.postureHistory.shift();
    }
    if (this.actionHistory.length > this.maxHistorySize) {
      this.actionHistory.shift();
    }
    if (this.angleHistory.length > this.maxHistorySize) {
      this.angleHistory.shift();
    }
  }

  /**
   * 重置分析器
   */
  reset(): void {
    this.postureHistory = [];
    this.actionHistory = [];
    this.angleHistory = [];
  }

  /**
   * 设置参数
   */
  setParameters(params: {
    maxHistorySize?: number;
    stabilityThreshold?: number;
  }): void {
    if (params.maxHistorySize !== undefined) {
      this.maxHistorySize = params.maxHistorySize;
    }
    if (params.stabilityThreshold !== undefined) {
      this.stabilityThreshold = params.stabilityThreshold;
    }
  }
}
