/**
 * 动画节点集合
 * 提供关键帧动画、补间动画、动画控制等功能的节点
 */
import { VisualScriptNode } from '../../visualscript/VisualScriptNode';
import { Debug } from '../../utils/Debug';
import { Vector3, Quaternion, Color } from 'three';
import { Entity } from '../entity/EntityNodes';

/**
 * 动画曲线类型
 */
export enum AnimationCurveType {
  LINEAR = 'linear',
  EASE_IN = 'easeIn',
  EASE_OUT = 'easeOut',
  EASE_IN_OUT = 'easeInOut',
  BOUNCE = 'bounce',
  ELASTIC = 'elastic',
  BACK = 'back',
  CUBIC_BEZIER = 'cubicBezier'
}

/**
 * 动画状态
 */
export enum AnimationState {
  STOPPED = 'stopped',
  PLAYING = 'playing',
  PAUSED = 'paused',
  COMPLETED = 'completed'
}

/**
 * 关键帧接口
 */
export interface Keyframe {
  time: number;
  value: any;
  easing: AnimationCurveType;
  tangentIn?: Vector3;
  tangentOut?: Vector3;
}

/**
 * 动画轨道接口
 */
export interface AnimationTrack {
  property: string;
  keyframes: Keyframe[];
  interpolation: 'linear' | 'step' | 'cubic';
}

/**
 * 动画剪辑接口
 */
export interface AnimationClip {
  name: string;
  duration: number;
  tracks: AnimationTrack[];
  loop: boolean;
  speed: number;
}

/**
 * 动画实例接口
 */
export interface AnimationInstance {
  id: string;
  clip: AnimationClip;
  entity: Entity;
  state: AnimationState;
  currentTime: number;
  speed: number;
  weight: number;
  startTime: number;
  endTime: number;
  loop: boolean;
  pingPong: boolean;
  onComplete?: () => void;
  onLoop?: () => void;
}

/**
 * 补间动画节点
 */
export class TweenNode extends VisualScriptNode {
  public static readonly TYPE = 'Tween';
  public static readonly NAME = '补间动画';
  public static readonly DESCRIPTION = '创建属性补间动画';

  private activeAnimations: Map<string, any> = new Map();

  constructor(nodeType: string = TweenNode.TYPE, name: string = TweenNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('start', 'trigger', '开始');
    this.addInput('stop', 'trigger', '停止');
    this.addInput('pause', 'trigger', '暂停');
    this.addInput('resume', 'trigger', '恢复');
    this.addInput('entity', 'object', '目标实体');
    this.addInput('property', 'string', '属性名');
    this.addInput('fromValue', 'any', '起始值');
    this.addInput('toValue', 'any', '目标值');
    this.addInput('duration', 'number', '持续时间');
    this.addInput('easing', 'string', '缓动类型');
    this.addInput('delay', 'number', '延迟时间');
    this.addInput('loop', 'boolean', '循环播放');
    this.addInput('pingPong', 'boolean', '往返播放');

    // 输出端口
    this.addOutput('currentValue', 'any', '当前值');
    this.addOutput('progress', 'number', '进度');
    this.addOutput('isPlaying', 'boolean', '正在播放');
    this.addOutput('animationId', 'string', '动画ID');
    this.addOutput('onStart', 'trigger', '开始时');
    this.addOutput('onUpdate', 'trigger', '更新时');
    this.addOutput('onComplete', 'trigger', '完成时');
    this.addOutput('onLoop', 'trigger', '循环时');
  }

  public execute(inputs?: any): any {
    try {
      const startTrigger = inputs?.start;
      const stopTrigger = inputs?.stop;
      const pauseTrigger = inputs?.pause;
      const resumeTrigger = inputs?.resume;
      const entity = inputs?.entity as Entity;
      const property = inputs?.property as string;

      if (startTrigger && entity && property) {
        return this.startTween(inputs);
      } else if (stopTrigger) {
        return this.stopTween(inputs);
      } else if (pauseTrigger) {
        return this.pauseTween(inputs);
      } else if (resumeTrigger) {
        return this.resumeTween(inputs);
      }

      return this.getDefaultOutputs();

    } catch (error) {
      Debug.error('TweenNode', '补间动画执行失败', error);
      return this.getDefaultOutputs();
    }
  }

  private startTween(inputs: any): any {
    const entity = inputs.entity as Entity;
    const property = inputs.property as string;
    const fromValue = inputs.fromValue;
    const toValue = inputs.toValue;
    const duration = inputs.duration as number || 1.0;
    const easing = inputs.easing as string || 'linear';
    const delay = inputs.delay as number || 0;
    const loop = inputs.loop as boolean || false;
    const pingPong = inputs.pingPong as boolean || false;

    const animationId = `${entity.id}_${property}_${Date.now()}`;

    // 获取起始值
    const startValue = fromValue !== undefined ? fromValue : this.getPropertyValue(entity, property);
    
    const animation = {
      id: animationId,
      entity,
      property,
      startValue,
      endValue: toValue,
      duration,
      easing,
      delay,
      loop,
      pingPong,
      startTime: Date.now() + delay * 1000,
      currentTime: 0,
      isPlaying: true,
      isPaused: false,
      direction: 1 // 1 for forward, -1 for backward
    };

    this.activeAnimations.set(animationId, animation);

    Debug.log('TweenNode', `补间动画开始: ${entity.name}.${property}`);

    return {
      currentValue: startValue,
      progress: 0,
      isPlaying: true,
      animationId,
      onStart: true,
      onUpdate: false,
      onComplete: false,
      onLoop: false
    };
  }

  private stopTween(inputs: any): any {
    const animationId = inputs.animationId as string;
    
    if (animationId && this.activeAnimations.has(animationId)) {
      this.activeAnimations.delete(animationId);
      Debug.log('TweenNode', `补间动画停止: ${animationId}`);
    }

    return this.getDefaultOutputs();
  }

  private pauseTween(inputs: any): any {
    const animationId = inputs.animationId as string;
    const animation = this.activeAnimations.get(animationId);
    
    if (animation) {
      animation.isPaused = true;
      Debug.log('TweenNode', `补间动画暂停: ${animationId}`);
    }

    return this.getDefaultOutputs();
  }

  private resumeTween(inputs: any): any {
    const animationId = inputs.animationId as string;
    const animation = this.activeAnimations.get(animationId);
    
    if (animation) {
      animation.isPaused = false;
      Debug.log('TweenNode', `补间动画恢复: ${animationId}`);
    }

    return this.getDefaultOutputs();
  }

  private getPropertyValue(entity: Entity, property: string): any {
    const parts = property.split('.');
    let current: any = entity;
    
    for (const part of parts) {
      if (current && typeof current === 'object' && part in current) {
        current = current[part];
      } else {
        return undefined;
      }
    }
    
    return current;
  }

  private setPropertyValue(entity: Entity, property: string, value: any): void {
    const parts = property.split('.');
    let current: any = entity;
    
    for (let i = 0; i < parts.length - 1; i++) {
      const part = parts[i];
      if (current && typeof current === 'object' && part in current) {
        current = current[part];
      } else {
        return;
      }
    }
    
    if (current && typeof current === 'object') {
      current[parts[parts.length - 1]] = value;
    }
  }

  private interpolateValue(from: any, to: any, t: number): any {
    if (typeof from === 'number' && typeof to === 'number') {
      return from + (to - from) * t;
    } else if (from instanceof Vector3 && to instanceof Vector3) {
      return from.clone().lerp(to, t);
    } else if (from instanceof Quaternion && to instanceof Quaternion) {
      return from.clone().slerp(to, t);
    } else if (from instanceof Color && to instanceof Color) {
      return from.clone().lerp(to, t);
    }
    
    // 默认情况下，在t >= 0.5时切换到目标值
    return t >= 0.5 ? to : from;
  }

  private applyEasing(t: number, easing: string): number {
    switch (easing) {
      case 'linear':
        return t;
      case 'easeIn':
        return t * t;
      case 'easeOut':
        return 1 - (1 - t) * (1 - t);
      case 'easeInOut':
        return t < 0.5 ? 2 * t * t : 1 - Math.pow(-2 * t + 2, 2) / 2;
      case 'bounce':
        return this.bounceEasing(t);
      case 'elastic':
        return this.elasticEasing(t);
      case 'back':
        return this.backEasing(t);
      default:
        return t;
    }
  }

  private bounceEasing(t: number): number {
    const n1 = 7.5625;
    const d1 = 2.75;

    if (t < 1 / d1) {
      return n1 * t * t;
    } else if (t < 2 / d1) {
      return n1 * (t -= 1.5 / d1) * t + 0.75;
    } else if (t < 2.5 / d1) {
      return n1 * (t -= 2.25 / d1) * t + 0.9375;
    } else {
      return n1 * (t -= 2.625 / d1) * t + 0.984375;
    }
  }

  private elasticEasing(t: number): number {
    const c4 = (2 * Math.PI) / 3;
    return t === 0 ? 0 : t === 1 ? 1 : -Math.pow(2, 10 * t - 10) * Math.sin((t * 10 - 10.75) * c4);
  }

  private backEasing(t: number): number {
    const c1 = 1.70158;
    const c3 = c1 + 1;
    return c3 * t * t * t - c1 * t * t;
  }

  private getDefaultOutputs(): any {
    return {
      currentValue: undefined,
      progress: 0,
      isPlaying: false,
      animationId: '',
      onStart: false,
      onUpdate: false,
      onComplete: false,
      onLoop: false
    };
  }

  // 更新方法，需要在每帧调用
  public update(): void {
    const currentTime = Date.now();
    const completedAnimations: string[] = [];

    for (const [id, animation] of this.activeAnimations) {
      if (!animation.isPlaying || animation.isPaused) {
        continue;
      }

      if (currentTime < animation.startTime) {
        continue; // 还在延迟期
      }

      const elapsed = (currentTime - animation.startTime) / 1000;
      let progress = Math.min(elapsed / animation.duration, 1);

      // 应用缓动
      const easedProgress = this.applyEasing(progress, animation.easing);

      // 处理往返播放
      if (animation.pingPong && animation.direction === -1) {
        progress = 1 - progress;
      }

      // 插值计算当前值
      const currentValue = this.interpolateValue(animation.startValue, animation.endValue, easedProgress);
      
      // 设置属性值
      this.setPropertyValue(animation.entity, animation.property, currentValue);

      // 检查是否完成
      if (elapsed >= animation.duration) {
        if (animation.loop) {
          if (animation.pingPong) {
            animation.direction *= -1;
            animation.startTime = currentTime;
          } else {
            animation.startTime = currentTime;
          }
        } else {
          completedAnimations.push(id);
        }
      }
    }

    // 清理完成的动画
    for (const id of completedAnimations) {
      this.activeAnimations.delete(id);
    }
  }
}

/**
 * 关键帧动画节点
 */
export class KeyframeAnimationNode extends VisualScriptNode {
  public static readonly TYPE = 'KeyframeAnimation';
  public static readonly NAME = '关键帧动画';
  public static readonly DESCRIPTION = '播放关键帧动画剪辑';

  constructor(nodeType: string = KeyframeAnimationNode.TYPE, name: string = KeyframeAnimationNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('play', 'trigger', '播放');
    this.addInput('stop', 'trigger', '停止');
    this.addInput('pause', 'trigger', '暂停');
    this.addInput('entity', 'object', '目标实体');
    this.addInput('animationClip', 'object', '动画剪辑');
    this.addInput('speed', 'number', '播放速度');
    this.addInput('startTime', 'number', '开始时间');
    this.addInput('endTime', 'number', '结束时间');
    this.addInput('loop', 'boolean', '循环播放');
    this.addInput('weight', 'number', '权重');

    // 输出端口
    this.addOutput('isPlaying', 'boolean', '正在播放');
    this.addOutput('currentTime', 'number', '当前时间');
    this.addOutput('progress', 'number', '播放进度');
    this.addOutput('animationState', 'string', '动画状态');
    this.addOutput('onPlay', 'trigger', '播放时');
    this.addOutput('onStop', 'trigger', '停止时');
    this.addOutput('onComplete', 'trigger', '完成时');
    this.addOutput('onLoop', 'trigger', '循环时');
  }

  public execute(inputs?: any): any {
    try {
      const playTrigger = inputs?.play;
      const stopTrigger = inputs?.stop;
      const pauseTrigger = inputs?.pause;
      const entity = inputs?.entity as Entity;
      const animationClip = inputs?.animationClip as AnimationClip;

      if (playTrigger && entity && animationClip) {
        return this.playAnimation(inputs);
      } else if (stopTrigger) {
        return this.stopAnimation(inputs);
      } else if (pauseTrigger) {
        return this.pauseAnimation(inputs);
      }

      return this.getDefaultOutputs();

    } catch (error) {
      Debug.error('KeyframeAnimationNode', '关键帧动画执行失败', error);
      return this.getDefaultOutputs();
    }
  }

  private playAnimation(inputs: any): any {
    const entity = inputs.entity as Entity;
    const animationClip = inputs.animationClip as AnimationClip;
    const speed = inputs.speed as number || 1.0;
    const startTime = inputs.startTime as number || 0;
    const endTime = inputs.endTime as number || animationClip.duration;
    const loop = inputs.loop as boolean || animationClip.loop;
    const weight = inputs.weight as number || 1.0;

    // 创建动画实例
    const animationInstance: AnimationInstance = {
      id: `${entity.id}_${animationClip.name}_${Date.now()}`,
      clip: animationClip,
      entity,
      state: AnimationState.PLAYING,
      currentTime: startTime,
      speed,
      weight,
      startTime,
      endTime,
      loop,
      pingPong: false
    };

    // 这里应该将动画实例添加到动画系统中
    // 简化实现，直接返回结果
    Debug.log('KeyframeAnimationNode', `关键帧动画开始: ${entity.name} - ${animationClip.name}`);

    return {
      isPlaying: true,
      currentTime: startTime,
      progress: 0,
      animationState: AnimationState.PLAYING,
      onPlay: true,
      onStop: false,
      onComplete: false,
      onLoop: false
    };
  }

  private stopAnimation(inputs: any): any {
    Debug.log('KeyframeAnimationNode', '关键帧动画停止');
    
    return {
      isPlaying: false,
      currentTime: 0,
      progress: 0,
      animationState: AnimationState.STOPPED,
      onPlay: false,
      onStop: true,
      onComplete: false,
      onLoop: false
    };
  }

  private pauseAnimation(inputs: any): any {
    Debug.log('KeyframeAnimationNode', '关键帧动画暂停');
    
    return {
      isPlaying: false,
      currentTime: 0,
      progress: 0,
      animationState: AnimationState.PAUSED,
      onPlay: false,
      onStop: false,
      onComplete: false,
      onLoop: false
    };
  }

  private getDefaultOutputs(): any {
    return {
      isPlaying: false,
      currentTime: 0,
      progress: 0,
      animationState: AnimationState.STOPPED,
      onPlay: false,
      onStop: false,
      onComplete: false,
      onLoop: false
    };
  }
}
