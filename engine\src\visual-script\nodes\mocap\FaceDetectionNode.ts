/**
 * 面部检测节点
 * 使用MediaPipe进行面部关键点检测和表情识别
 */
import { VisualScriptNode } from '../../../visualscript/VisualScriptNode';
import { Debug } from '../../../utils/Debug';
import { Vector3 } from 'three';

/**
 * 面部关键点数据接口
 */
export interface FaceLandmarkData {
  x: number;
  y: number;
  z?: number;
  visibility?: number;
}

/**
 * 表情类型枚举
 */
export enum ExpressionType {
  NEUTRAL = 'neutral',
  HAPPY = 'happy',
  SAD = 'sad',
  ANGRY = 'angry',
  SURPRISED = 'surprised',
  DISGUSTED = 'disgusted',
  FEARFUL = 'fearful',
  CONTEMPT = 'contempt'
}

/**
 * 面部表情结果
 */
export interface ExpressionResult {
  type: ExpressionType;
  confidence: number;
  intensity: number;
  timestamp: number;
}

/**
 * 面部检测结果
 */
export interface FaceResults {
  landmarks?: FaceLandmarkData[];
  expressions?: ExpressionResult[];
  faceBox?: { x: number; y: number; width: number; height: number };
  confidence: number;
  processingTime: number;
  timestamp: number;
}

/**
 * MediaPipe面部配置
 */
export interface MediaPipeFaceConfig {
  maxNumFaces: number;
  refineLandmarks: boolean;
  minDetectionConfidence: number;
  minTrackingConfidence: number;
  staticImageMode: boolean;
}

/**
 * 面部表情分析器
 */
class FacialExpressionAnalyzer {
  private expressionHistory: Map<ExpressionType, number[]> = new Map();
  private maxHistorySize = 10;

  /**
   * MediaPipe面部关键点索引
   */
  private static readonly FACE_LANDMARKS = {
    // 眼部区域
    LEFT_EYE: [33, 7, 163, 144, 145, 153, 154, 155, 133, 173, 157, 158, 159, 160, 161, 246],
    RIGHT_EYE: [362, 382, 381, 380, 374, 373, 390, 249, 263, 466, 388, 387, 386, 385, 384, 398],
    
    // 眉毛区域
    LEFT_EYEBROW: [46, 53, 52, 51, 48, 115, 131, 134, 102, 49, 220, 305],
    RIGHT_EYEBROW: [276, 283, 282, 281, 278, 344, 360, 363, 331, 279, 440, 75],
    
    // 嘴部区域
    LIPS_OUTER: [61, 84, 17, 314, 405, 320, 307, 375, 321, 308, 324, 318],
    LIPS_INNER: [78, 95, 88, 178, 87, 14, 317, 402, 318, 324, 308, 415],
    
    // 鼻子区域
    NOSE: [1, 2, 5, 4, 6, 19, 94, 168, 8, 9, 10, 151, 195, 197, 196, 3],
    
    // 面部轮廓
    FACE_OVAL: [10, 338, 297, 332, 284, 251, 389, 356, 454, 323, 361, 288, 397, 365, 379, 378, 400, 377, 152, 148, 176, 149, 150, 136, 172, 58, 132, 93, 234, 127, 162, 21, 54, 103, 67, 109]
  };

  /**
   * 分析面部表情
   */
  analyzeExpression(landmarks: FaceLandmarkData[]): ExpressionResult[] {
    const expressions: ExpressionResult[] = [];

    // 分析各种表情
    expressions.push(this.analyzeHappiness(landmarks));
    expressions.push(this.analyzeSadness(landmarks));
    expressions.push(this.analyzeAnger(landmarks));
    expressions.push(this.analyzeSurprise(landmarks));

    // 应用时间平滑
    return expressions.map(expr => this.applySmoothingFilter(expr));
  }

  /**
   * 分析快乐表情
   */
  private analyzeHappiness(landmarks: FaceLandmarkData[]): ExpressionResult {
    // 分析嘴角上扬
    const leftMouthCorner = landmarks[61];
    const rightMouthCorner = landmarks[291];
    const upperLip = landmarks[13];
    const lowerLip = landmarks[14];

    // 计算嘴角上扬程度
    const mouthCurvature = this.calculateMouthCurvature(leftMouthCorner, rightMouthCorner, upperLip, lowerLip);
    
    // 分析眼部笑容（眼角下垂）
    const eyeSmile = this.calculateEyeSmile(landmarks);
    
    const confidence = Math.min((mouthCurvature + eyeSmile) / 2, 1.0);
    
    return {
      type: ExpressionType.HAPPY,
      confidence,
      intensity: confidence,
      timestamp: Date.now()
    };
  }

  /**
   * 分析悲伤表情
   */
  private analyzeSadness(landmarks: FaceLandmarkData[]): ExpressionResult {
    // 分析嘴角下垂
    const leftMouthCorner = landmarks[61];
    const rightMouthCorner = landmarks[291];
    const upperLip = landmarks[13];
    
    // 计算嘴角下垂程度
    const mouthDroop = this.calculateMouthDroop(leftMouthCorner, rightMouthCorner, upperLip);
    
    // 分析眉毛下垂
    const eyebrowDroop = this.calculateEyebrowDroop(landmarks);
    
    const confidence = Math.min((mouthDroop + eyebrowDroop) / 2, 1.0);
    
    return {
      type: ExpressionType.SAD,
      confidence,
      intensity: confidence,
      timestamp: Date.now()
    };
  }

  /**
   * 分析愤怒表情
   */
  private analyzeAnger(landmarks: FaceLandmarkData[]): ExpressionResult {
    // 分析眉毛紧皱
    const eyebrowFurrow = this.calculateEyebrowFurrow(landmarks);
    
    // 分析嘴部紧绷
    const mouthTension = this.calculateMouthTension(landmarks);
    
    const confidence = Math.min((eyebrowFurrow + mouthTension) / 2, 1.0);
    
    return {
      type: ExpressionType.ANGRY,
      confidence,
      intensity: confidence,
      timestamp: Date.now()
    };
  }

  /**
   * 分析惊讶表情
   */
  private analyzeSurprise(landmarks: FaceLandmarkData[]): ExpressionResult {
    // 分析眉毛上扬
    const eyebrowRaise = this.calculateEyebrowRaise(landmarks);
    
    // 分析眼睛睁大
    const eyeWidening = this.calculateEyeWidening(landmarks);
    
    // 分析嘴巴张开
    const mouthOpening = this.calculateMouthOpening(landmarks);
    
    const confidence = Math.min((eyebrowRaise + eyeWidening + mouthOpening) / 3, 1.0);
    
    return {
      type: ExpressionType.SURPRISED,
      confidence,
      intensity: confidence,
      timestamp: Date.now()
    };
  }

  /**
   * 计算嘴角弯曲度
   */
  private calculateMouthCurvature(leftCorner: FaceLandmarkData, rightCorner: FaceLandmarkData, upperLip: FaceLandmarkData, lowerLip: FaceLandmarkData): number {
    const mouthCenter = {
      x: (upperLip.x + lowerLip.x) / 2,
      y: (upperLip.y + lowerLip.y) / 2
    };
    
    const leftCurvature = leftCorner.y - mouthCenter.y;
    const rightCurvature = rightCorner.y - mouthCenter.y;
    
    // 负值表示上扬（快乐），正值表示下垂（悲伤）
    return Math.max(0, -(leftCurvature + rightCurvature) / 2);
  }

  /**
   * 计算眼部笑容
   */
  private calculateEyeSmile(landmarks: FaceLandmarkData[]): number {
    // 简化的眼部笑容检测
    const leftEyeTop = landmarks[159];
    const leftEyeBottom = landmarks[145];
    const rightEyeTop = landmarks[386];
    const rightEyeBottom = landmarks[374];
    
    const leftEyeHeight = Math.abs(leftEyeTop.y - leftEyeBottom.y);
    const rightEyeHeight = Math.abs(rightEyeTop.y - rightEyeBottom.y);
    
    // 眼睛变小表示笑容
    const averageEyeHeight = (leftEyeHeight + rightEyeHeight) / 2;
    return Math.max(0, 1 - averageEyeHeight * 10); // 调整系数
  }

  /**
   * 计算嘴角下垂
   */
  private calculateMouthDroop(leftCorner: FaceLandmarkData, rightCorner: FaceLandmarkData, upperLip: FaceLandmarkData): number {
    const mouthCenter = upperLip.y;
    const leftDroop = leftCorner.y - mouthCenter;
    const rightDroop = rightCorner.y - mouthCenter;
    
    // 正值表示下垂
    return Math.max(0, (leftDroop + rightDroop) / 2);
  }

  /**
   * 计算眉毛下垂
   */
  private calculateEyebrowDroop(landmarks: FaceLandmarkData[]): number {
    // 简化的眉毛下垂检测
    const leftEyebrow = landmarks[70];
    const rightEyebrow = landmarks[300];
    const noseBridge = landmarks[9];
    
    const leftDistance = Math.abs(leftEyebrow.y - noseBridge.y);
    const rightDistance = Math.abs(rightEyebrow.y - noseBridge.y);
    
    // 距离变小表示眉毛下垂
    const averageDistance = (leftDistance + rightDistance) / 2;
    return Math.max(0, 1 - averageDistance * 5); // 调整系数
  }

  /**
   * 计算眉毛紧皱
   */
  private calculateEyebrowFurrow(landmarks: FaceLandmarkData[]): number {
    // 简化的眉毛紧皱检测
    const leftInnerEyebrow = landmarks[55];
    const rightInnerEyebrow = landmarks[285];
    
    const eyebrowDistance = Math.abs(leftInnerEyebrow.x - rightInnerEyebrow.x);
    
    // 距离变小表示眉毛紧皱
    return Math.max(0, 1 - eyebrowDistance * 10); // 调整系数
  }

  /**
   * 计算嘴部紧绷
   */
  private calculateMouthTension(landmarks: FaceLandmarkData[]): number {
    // 简化的嘴部紧绷检测
    const leftCorner = landmarks[61];
    const rightCorner = landmarks[291];
    const upperLip = landmarks[13];
    const lowerLip = landmarks[14];
    
    const mouthWidth = Math.abs(leftCorner.x - rightCorner.x);
    const mouthHeight = Math.abs(upperLip.y - lowerLip.y);
    
    const aspectRatio = mouthHeight / mouthWidth;
    
    // 宽高比变小表示嘴部紧绷
    return Math.max(0, 1 - aspectRatio * 5); // 调整系数
  }

  /**
   * 计算眉毛上扬
   */
  private calculateEyebrowRaise(landmarks: FaceLandmarkData[]): number {
    // 简化的眉毛上扬检测
    const leftEyebrow = landmarks[70];
    const rightEyebrow = landmarks[300];
    const noseBridge = landmarks[9];
    
    const leftDistance = Math.abs(leftEyebrow.y - noseBridge.y);
    const rightDistance = Math.abs(rightEyebrow.y - noseBridge.y);
    
    // 距离变大表示眉毛上扬
    const averageDistance = (leftDistance + rightDistance) / 2;
    return Math.min(1, averageDistance * 3); // 调整系数
  }

  /**
   * 计算眼睛睁大
   */
  private calculateEyeWidening(landmarks: FaceLandmarkData[]): number {
    // 简化的眼睛睁大检测
    const leftEyeTop = landmarks[159];
    const leftEyeBottom = landmarks[145];
    const rightEyeTop = landmarks[386];
    const rightEyeBottom = landmarks[374];
    
    const leftEyeHeight = Math.abs(leftEyeTop.y - leftEyeBottom.y);
    const rightEyeHeight = Math.abs(rightEyeTop.y - rightEyeBottom.y);
    
    // 眼睛变大表示睁大
    const averageEyeHeight = (leftEyeHeight + rightEyeHeight) / 2;
    return Math.min(1, averageEyeHeight * 8); // 调整系数
  }

  /**
   * 计算嘴巴张开
   */
  private calculateMouthOpening(landmarks: FaceLandmarkData[]): number {
    // 简化的嘴巴张开检测
    const upperLip = landmarks[13];
    const lowerLip = landmarks[14];
    
    const mouthHeight = Math.abs(upperLip.y - lowerLip.y);
    
    // 嘴巴高度变大表示张开
    return Math.min(1, mouthHeight * 15); // 调整系数
  }

  /**
   * 应用平滑滤波
   */
  private applySmoothingFilter(expression: ExpressionResult): ExpressionResult {
    if (!this.expressionHistory.has(expression.type)) {
      this.expressionHistory.set(expression.type, []);
    }

    const history = this.expressionHistory.get(expression.type)!;
    history.push(expression.confidence);

    if (history.length > this.maxHistorySize) {
      history.shift();
    }

    // 计算平滑后的置信度
    const smoothedConfidence = history.reduce((sum, val) => sum + val, 0) / history.length;

    return {
      ...expression,
      confidence: smoothedConfidence,
      intensity: smoothedConfidence
    };
  }

  /**
   * 重置历史记录
   */
  reset(): void {
    this.expressionHistory.clear();
  }
}

/**
 * 真实的MediaPipe面部检测器
 */
class RealMediaPipeFaceDetector {
  private faceMesh: any = null;
  private config: MediaPipeFaceConfig;
  private isInitialized = false;
  private eventListeners: Map<string, Function[]> = new Map();
  private canvas: HTMLCanvasElement;
  private context: CanvasRenderingContext2D;
  private expressionAnalyzer: FacialExpressionAnalyzer;

  constructor(config: MediaPipeFaceConfig) {
    this.config = config;
    this.expressionAnalyzer = new FacialExpressionAnalyzer();

    // 创建离屏画布用于处理
    this.canvas = document.createElement('canvas');
    this.context = this.canvas.getContext('2d')!;
  }

  /**
   * 初始化MediaPipe Face Mesh
   */
  async initialize(): Promise<void> {
    try {
      // 检查MediaPipe是否可用
      if (typeof window === 'undefined' || !(window as any).FaceMesh) {
        throw new Error('MediaPipe FaceMesh库未加载，请确保已引入MediaPipe脚本');
      }

      const FaceMesh = (window as any).FaceMesh;

      // 创建FaceMesh实例
      this.faceMesh = new FaceMesh({
        locateFile: (file: string) => {
          return `https://cdn.jsdelivr.net/npm/@mediapipe/face_mesh/${file}`;
        }
      });

      // 配置选项
      await this.faceMesh.setOptions({
        maxNumFaces: this.config.maxNumFaces,
        refineLandmarks: this.config.refineLandmarks,
        minDetectionConfidence: this.config.minDetectionConfidence,
        minTrackingConfidence: this.config.minTrackingConfidence,
        staticImageMode: this.config.staticImageMode
      });

      // 设置结果回调
      this.faceMesh.onResults((results: any) => {
        this.handleResults(results);
      });

      this.isInitialized = true;
      this.emit('initialized');

      Debug.log('MediaPipeFaceDetector', 'MediaPipe面部检测器初始化成功');

    } catch (error) {
      Debug.error('MediaPipeFaceDetector', 'MediaPipe初始化失败', error);
      this.isInitialized = false;
      this.emit('error', error);
      throw error;
    }
  }

  /**
   * 处理MediaPipe结果
   */
  private handleResults(results: any): void {
    const startTime = performance.now();

    try {
      const faceResults: FaceResults = {
        landmarks: undefined,
        expressions: undefined,
        faceBox: undefined,
        confidence: 0,
        processingTime: 0,
        timestamp: Date.now()
      };

      // 处理面部关键点
      if (results.multiFaceLandmarks && results.multiFaceLandmarks.length > 0) {
        const landmarks = results.multiFaceLandmarks[0];

        faceResults.landmarks = landmarks.map((landmark: any) => ({
          x: landmark.x,
          y: landmark.y,
          z: landmark.z,
          visibility: landmark.visibility || 1.0
        }));

        // 计算面部边界框
        faceResults.faceBox = this.calculateFaceBoundingBox(faceResults.landmarks);

        // 分析表情
        faceResults.expressions = this.expressionAnalyzer.analyzeExpression(faceResults.landmarks);

        // 计算平均置信度
        faceResults.confidence = faceResults.landmarks.reduce((sum, landmark) =>
          sum + (landmark.visibility || 0), 0) / faceResults.landmarks.length;
      }

      faceResults.processingTime = performance.now() - startTime;
      this.emit('results', faceResults);

    } catch (error) {
      Debug.error('MediaPipeFaceDetector', '处理结果失败', error);
      this.emit('error', error);
    }
  }

  /**
   * 计算面部边界框
   */
  private calculateFaceBoundingBox(landmarks: FaceLandmarkData[]): { x: number; y: number; width: number; height: number } {
    let minX = Infinity, minY = Infinity, maxX = -Infinity, maxY = -Infinity;

    for (const landmark of landmarks) {
      minX = Math.min(minX, landmark.x);
      minY = Math.min(minY, landmark.y);
      maxX = Math.max(maxX, landmark.x);
      maxY = Math.max(maxY, landmark.y);
    }

    return {
      x: minX,
      y: minY,
      width: maxX - minX,
      height: maxY - minY
    };
  }

  /**
   * 检测面部
   */
  async detectFace(imageData: ImageData): Promise<FaceResults> {
    if (!this.isInitialized || !this.faceMesh) {
      throw new Error('MediaPipe面部检测器未初始化');
    }

    try {
      // 将ImageData转换为HTMLImageElement
      this.canvas.width = imageData.width;
      this.canvas.height = imageData.height;
      this.context.putImageData(imageData, 0, 0);

      // 发送到MediaPipe进行处理
      await this.faceMesh.send({ image: this.canvas });

      // 返回一个Promise，等待结果回调
      return new Promise((resolve, reject) => {
        const timeout = setTimeout(() => {
          reject(new Error('检测超时'));
        }, 5000);

        const onResults = (results: FaceResults) => {
          clearTimeout(timeout);
          this.off('results', onResults);
          this.off('error', onError);
          resolve(results);
        };

        const onError = (error: any) => {
          clearTimeout(timeout);
          this.off('results', onResults);
          this.off('error', onError);
          reject(error);
        };

        this.on('results', onResults);
        this.on('error', onError);
      });

    } catch (error) {
      Debug.error('MediaPipeFaceDetector', '面部检测失败', error);
      throw error;
    }
  }

  /**
   * 更新配置
   */
  async updateConfig(newConfig: Partial<MediaPipeFaceConfig>): Promise<void> {
    this.config = { ...this.config, ...newConfig };

    if (this.faceMesh && this.isInitialized) {
      try {
        await this.faceMesh.setOptions({
          maxNumFaces: this.config.maxNumFaces,
          refineLandmarks: this.config.refineLandmarks,
          minDetectionConfidence: this.config.minDetectionConfidence,
          minTrackingConfidence: this.config.minTrackingConfidence,
          staticImageMode: this.config.staticImageMode
        });

        Debug.log('MediaPipeFaceDetector', '配置更新成功');
      } catch (error) {
        Debug.error('MediaPipeFaceDetector', '配置更新失败', error);
        throw error;
      }
    }
  }

  /**
   * 销毁检测器
   */
  destroy(): void {
    if (this.faceMesh) {
      this.faceMesh.close();
      this.faceMesh = null;
    }

    this.isInitialized = false;
    this.expressionAnalyzer.reset();
    this.eventListeners.clear();

    Debug.log('MediaPipeFaceDetector', '检测器已销毁');
  }

  /**
   * 事件监听
   */
  on(event: string, callback: Function): void {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, []);
    }
    this.eventListeners.get(event)!.push(callback);
  }

  /**
   * 移除事件监听
   */
  off(event: string, callback: Function): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      const index = listeners.indexOf(callback);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    }
  }

  /**
   * 发出事件
   */
  private emit(event: string, data?: any): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      listeners.forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          Debug.error('MediaPipeFaceDetector', `事件回调执行失败: ${event}`, error);
        }
      });
    }
  }

  /**
   * 获取初始化状态
   */
  getInitialized(): boolean {
    return this.isInitialized;
  }

  /**
   * 检查MediaPipe是否可用
   */
  static isAvailable(): boolean {
    return typeof window !== 'undefined' && !!(window as any).FaceMesh;
  }
}

/**
 * 面部检测节点配置
 */
export interface FaceDetectionNodeConfig {
  /** 最大面部数量 */
  maxNumFaces: number;
  /** 是否精细化关键点 */
  refineLandmarks: boolean;
  /** 最小检测置信度 */
  minDetectionConfidence: number;
  /** 最小跟踪置信度 */
  minTrackingConfidence: number;
  /** 是否启用表情识别 */
  enableExpressionRecognition: boolean;
  /** 表情置信度阈值 */
  expressionConfidenceThreshold: number;
  /** 是否自动初始化 */
  autoInitialize: boolean;
  /** 是否启用静态图像模式 */
  staticImageMode: boolean;
}

/**
 * 面部检测节点
 */
export class FaceDetectionNode extends VisualScriptNode {
  /** 节点类型 */
  public static readonly TYPE = 'FaceDetection';

  /** 节点名称 */
  public static readonly NAME = '面部检测';

  /** 节点描述 */
  public static readonly DESCRIPTION = '使用MediaPipe检测面部关键点和识别表情，支持468个面部关键点';

  private faceDetector: RealMediaPipeFaceDetector | null = null;
  private config: FaceDetectionNodeConfig;
  private isInitialized = false;
  private lastResults: FaceResults | null = null;
  private processingCount = 0;
  private successCount = 0;
  private averageProcessingTime = 0;
  private isProcessing = false;

  /** 默认配置 */
  private static readonly DEFAULT_CONFIG: FaceDetectionNodeConfig = {
    maxNumFaces: 1,
    refineLandmarks: true,
    minDetectionConfidence: 0.5,
    minTrackingConfidence: 0.5,
    enableExpressionRecognition: true,
    expressionConfidenceThreshold: 0.6,
    autoInitialize: true,
    staticImageMode: false
  };

  constructor(nodeType: string = FaceDetectionNode.TYPE, name: string = FaceDetectionNode.NAME, id?: string) {
    super(nodeType, name, id);

    this.config = { ...FaceDetectionNode.DEFAULT_CONFIG };
    this.setupPorts();

    // 如果自动初始化，则立即初始化
    if (this.config.autoInitialize) {
      this.initializeDetector().catch(error => {
        Debug.error('FaceDetectionNode', '自动初始化失败', error);
      });
    }
  }

  /**
   * 设置输入输出端口
   */
  private setupPorts(): void {
    // 输入端口
    this.addInput('imageData', 'object', '图像数据');
    this.addInput('detect', 'trigger', '检测');
    this.addInput('initialize', 'trigger', '初始化');
    this.addInput('reset', 'trigger', '重置');
    this.addInput('maxNumFaces', 'number', '最大面部数量');
    this.addInput('minDetectionConfidence', 'number', '检测置信度');
    this.addInput('minTrackingConfidence', 'number', '跟踪置信度');
    this.addInput('enableExpressionRecognition', 'boolean', '启用表情识别');
    this.addInput('refineLandmarks', 'boolean', '精细化关键点');

    // 输出端口
    this.addOutput('landmarks', 'array', '面部关键点');
    this.addOutput('expressions', 'array', '表情识别结果');
    this.addOutput('faceBox', 'object', '面部边界框');
    this.addOutput('confidence', 'number', '置信度');
    this.addOutput('isDetected', 'boolean', '检测成功');
    this.addOutput('processingTime', 'number', '处理时间');
    this.addOutput('averageProcessingTime', 'number', '平均处理时间');
    this.addOutput('successRate', 'number', '成功率');
    this.addOutput('isProcessing', 'boolean', '正在处理');
    this.addOutput('isInitialized', 'boolean', '已初始化');

    // 表情输出端口
    this.addOutput('dominantExpression', 'string', '主要表情');
    this.addOutput('expressionIntensity', 'number', '表情强度');
    this.addOutput('happinessLevel', 'number', '快乐程度');
    this.addOutput('sadnessLevel', 'number', '悲伤程度');
    this.addOutput('angerLevel', 'number', '愤怒程度');
    this.addOutput('surpriseLevel', 'number', '惊讶程度');

    // 面部特征输出端口
    this.addOutput('eyeRegion', 'object', '眼部区域');
    this.addOutput('mouthRegion', 'object', '嘴部区域');
    this.addOutput('noseRegion', 'object', '鼻部区域');
    this.addOutput('eyebrowRegion', 'object', '眉毛区域');
    this.addOutput('faceContour', 'object', '面部轮廓');

    // 事件输出端口
    this.addOutput('onDetected', 'trigger', '检测完成');
    this.addOutput('onExpressionChanged', 'trigger', '表情变化');
    this.addOutput('onInitialized', 'trigger', '初始化完成');
    this.addOutput('onError', 'trigger', '错误');
  }

  /**
   * 执行节点
   */
  public async execute(inputs?: any): Promise<any> {
    try {
      // 检查输入
      const imageData = inputs?.imageData as ImageData;
      const detectTrigger = inputs?.detect;
      const initializeTrigger = inputs?.initialize;
      const resetTrigger = inputs?.reset;
      const maxNumFaces = inputs?.maxNumFaces as number;
      const minDetectionConfidence = inputs?.minDetectionConfidence as number;
      const minTrackingConfidence = inputs?.minTrackingConfidence as number;
      const enableExpressionRecognition = inputs?.enableExpressionRecognition as boolean;
      const refineLandmarks = inputs?.refineLandmarks as boolean;

      // 更新配置
      let configChanged = false;
      if (maxNumFaces !== undefined && maxNumFaces !== this.config.maxNumFaces) {
        this.config.maxNumFaces = Math.max(1, Math.min(4, Math.floor(maxNumFaces)));
        configChanged = true;
      }
      if (minDetectionConfidence !== undefined && minDetectionConfidence !== this.config.minDetectionConfidence) {
        this.config.minDetectionConfidence = Math.max(0, Math.min(1, minDetectionConfidence));
        configChanged = true;
      }
      if (minTrackingConfidence !== undefined && minTrackingConfidence !== this.config.minTrackingConfidence) {
        this.config.minTrackingConfidence = Math.max(0, Math.min(1, minTrackingConfidence));
        configChanged = true;
      }
      if (enableExpressionRecognition !== undefined && enableExpressionRecognition !== this.config.enableExpressionRecognition) {
        this.config.enableExpressionRecognition = enableExpressionRecognition;
        configChanged = true;
      }
      if (refineLandmarks !== undefined && refineLandmarks !== this.config.refineLandmarks) {
        this.config.refineLandmarks = refineLandmarks;
        configChanged = true;
      }

      // 如果配置改变且检测器已初始化，更新配置
      if (configChanged && this.isInitialized && this.faceDetector) {
        await this.faceDetector.updateConfig({
          maxNumFaces: this.config.maxNumFaces,
          refineLandmarks: this.config.refineLandmarks,
          minDetectionConfidence: this.config.minDetectionConfidence,
          minTrackingConfidence: this.config.minTrackingConfidence,
          staticImageMode: this.config.staticImageMode
        });
      }

      // 处理重置触发
      if (resetTrigger) {
        await this.resetDetector();
      }

      // 处理初始化触发
      if (initializeTrigger || (!this.isInitialized && this.config.autoInitialize)) {
        await this.initializeDetector();
      }

      // 处理检测触发
      if (detectTrigger && imageData && this.isInitialized && !this.isProcessing) {
        await this.detectFace(imageData);
      }

      // 返回输出
      return this.getNodeOutputs();

    } catch (error) {
      Debug.error('FaceDetectionNode', '节点执行失败', String(error));
      return {
        onError: true,
        isInitialized: this.isInitialized,
        isProcessing: this.isProcessing
      };
    }
  }

  /**
   * 初始化检测器
   */
  private async initializeDetector(): Promise<void> {
    try {
      if (this.isInitialized) {
        Debug.log('FaceDetectionNode', '检测器已初始化');
        return;
      }

      // 检查MediaPipe是否可用
      if (!RealMediaPipeFaceDetector.isAvailable()) {
        throw new Error('MediaPipe FaceMesh库未加载，请确保已引入MediaPipe脚本');
      }

      // 创建检测器
      this.faceDetector = new RealMediaPipeFaceDetector({
        maxNumFaces: this.config.maxNumFaces,
        refineLandmarks: this.config.refineLandmarks,
        minDetectionConfidence: this.config.minDetectionConfidence,
        minTrackingConfidence: this.config.minTrackingConfidence,
        staticImageMode: this.config.staticImageMode
      });

      // 设置事件监听
      this.setupDetectorEvents();

      // 初始化
      await this.faceDetector.initialize();

      this.isInitialized = true;
      this.processingCount = 0;
      this.successCount = 0;
      this.averageProcessingTime = 0;

      Debug.log('FaceDetectionNode', '面部检测器初始化成功');

    } catch (error) {
      Debug.error('FaceDetectionNode', '初始化检测器失败', String(error));
      this.isInitialized = false;
      throw error;
    }
  }

  /**
   * 重置检测器
   */
  private async resetDetector(): Promise<void> {
    try {
      if (this.faceDetector) {
        this.faceDetector.destroy();
        this.faceDetector = null;
      }

      this.isInitialized = false;
      this.lastResults = null;
      this.processingCount = 0;
      this.successCount = 0;
      this.averageProcessingTime = 0;
      this.isProcessing = false;

      Debug.log('FaceDetectionNode', '检测器已重置');

    } catch (error) {
      Debug.error('FaceDetectionNode', '重置检测器失败', String(error));
      throw error;
    }
  }

  /**
   * 设置检测器事件监听
   */
  private setupDetectorEvents(): void {
    if (!this.faceDetector) return;

    this.faceDetector.on('results', (results: FaceResults) => {
      this.lastResults = results;
      this.successCount++;

      // 更新平均处理时间
      this.averageProcessingTime = (this.averageProcessingTime * (this.processingCount - 1) + results.processingTime) / this.processingCount;

      this.isProcessing = false;
      Debug.log('FaceDetectionNode', `面部检测完成，置信度: ${results.confidence.toFixed(3)}`);
    });

    this.faceDetector.on('error', (error: any) => {
      this.isProcessing = false;
      Debug.error('FaceDetectionNode', '检测器错误', error);
    });

    this.faceDetector.on('initialized', () => {
      Debug.log('FaceDetectionNode', '检测器初始化事件');
    });
  }

  /**
   * 检测面部
   */
  private async detectFace(imageData: ImageData): Promise<void> {
    if (!this.isInitialized || !this.faceDetector) {
      Debug.error('FaceDetectionNode', '检测器未初始化');
      return;
    }

    if (this.isProcessing) {
      Debug.warn('FaceDetectionNode', '检测正在进行中，跳过此次检测');
      return;
    }

    try {
      this.isProcessing = true;
      this.processingCount++;

      // 执行检测
      const results = await this.faceDetector.detectFace(imageData);

      // 结果已在事件回调中处理
      Debug.log('FaceDetectionNode', '面部检测完成');

    } catch (error) {
      this.isProcessing = false;
      Debug.error('FaceDetectionNode', '面部检测失败', String(error));
      throw error;
    }
  }

  /**
   * 获取节点输出值
   */
  private getNodeOutputs(): any {
    const baseOutputs = {
      landmarks: [],
      expressions: [],
      faceBox: null,
      confidence: 0,
      isDetected: false,
      processingTime: 0,
      averageProcessingTime: this.averageProcessingTime,
      successRate: this.processingCount > 0 ? this.successCount / this.processingCount : 0,
      isProcessing: this.isProcessing,
      isInitialized: this.isInitialized,
      dominantExpression: '',
      expressionIntensity: 0,
      happinessLevel: 0,
      sadnessLevel: 0,
      angerLevel: 0,
      surpriseLevel: 0,
      eyeRegion: null,
      mouthRegion: null,
      noseRegion: null,
      eyebrowRegion: null,
      faceContour: null,
      onDetected: false,
      onExpressionChanged: false,
      onInitialized: false,
      onError: false
    };

    if (!this.lastResults) {
      return baseOutputs;
    }

    const results = this.lastResults;

    // 提取面部特征区域
    const faceRegions: any = {};
    if (results.landmarks && results.landmarks.length >= 468) {
      const landmarks = results.landmarks;

      // 眼部区域
      faceRegions.eyeRegion = {
        leftEye: landmarks.slice(33, 42),
        rightEye: landmarks.slice(362, 371)
      };

      // 嘴部区域
      faceRegions.mouthRegion = landmarks.slice(61, 81);

      // 鼻部区域
      faceRegions.noseRegion = landmarks.slice(1, 17);

      // 眉毛区域
      faceRegions.eyebrowRegion = {
        leftEyebrow: landmarks.slice(46, 58),
        rightEyebrow: landmarks.slice(276, 288)
      };

      // 面部轮廓
      faceRegions.faceContour = landmarks.slice(10, 152);
    }

    // 分析表情
    let dominantExpression = '';
    let expressionIntensity = 0;
    let happinessLevel = 0;
    let sadnessLevel = 0;
    let angerLevel = 0;
    let surpriseLevel = 0;

    if (results.expressions && results.expressions.length > 0) {
      // 找到置信度最高的表情
      const sortedExpressions = results.expressions.sort((a, b) => b.confidence - a.confidence);
      const dominant = sortedExpressions[0];

      if (dominant.confidence > this.config.expressionConfidenceThreshold) {
        dominantExpression = dominant.type;
        expressionIntensity = dominant.intensity;
      }

      // 提取各种表情的强度
      for (const expr of results.expressions) {
        switch (expr.type) {
          case ExpressionType.HAPPY:
            happinessLevel = expr.intensity;
            break;
          case ExpressionType.SAD:
            sadnessLevel = expr.intensity;
            break;
          case ExpressionType.ANGRY:
            angerLevel = expr.intensity;
            break;
          case ExpressionType.SURPRISED:
            surpriseLevel = expr.intensity;
            break;
        }
      }
    }

    return {
      landmarks: results.landmarks || [],
      expressions: results.expressions || [],
      faceBox: results.faceBox || null,
      confidence: results.confidence,
      isDetected: results.confidence > this.config.minDetectionConfidence,
      processingTime: results.processingTime,
      averageProcessingTime: this.averageProcessingTime,
      successRate: this.processingCount > 0 ? this.successCount / this.processingCount : 0,
      isProcessing: this.isProcessing,
      isInitialized: this.isInitialized,
      dominantExpression,
      expressionIntensity,
      happinessLevel,
      sadnessLevel,
      angerLevel,
      surpriseLevel,
      onDetected: false,
      onExpressionChanged: false,
      onInitialized: false,
      onError: false,
      ...faceRegions
    };
  }

  /**
   * 获取节点配置
   */
  public getConfig(): FaceDetectionNodeConfig {
    return { ...this.config };
  }

  /**
   * 更新节点配置
   */
  public async updateConfig(newConfig: Partial<FaceDetectionNodeConfig>): Promise<void> {
    const oldConfig = { ...this.config };
    this.config = { ...this.config, ...newConfig };

    // 如果检测器已初始化，更新其配置
    if (this.faceDetector && this.isInitialized) {
      try {
        await this.faceDetector.updateConfig({
          maxNumFaces: this.config.maxNumFaces,
          refineLandmarks: this.config.refineLandmarks,
          minDetectionConfidence: this.config.minDetectionConfidence,
          minTrackingConfidence: this.config.minTrackingConfidence,
          staticImageMode: this.config.staticImageMode
        });
        Debug.log('FaceDetectionNode', '配置更新成功', { oldConfig, newConfig: this.config });
      } catch (error) {
        Debug.error('FaceDetectionNode', '配置更新失败', error);
        throw error;
      }
    }
  }

  /**
   * 获取最后检测结果
   */
  public getLastResults(): FaceResults | null {
    return this.lastResults ? { ...this.lastResults } : null;
  }

  /**
   * 获取检测成功率
   */
  public getSuccessRate(): number {
    return this.processingCount > 0 ? this.successCount / this.processingCount : 0;
  }

  /**
   * 获取平均处理时间
   */
  public getAverageProcessingTime(): number {
    return this.averageProcessingTime;
  }

  /**
   * 是否已初始化
   */
  public get initialized(): boolean {
    return this.isInitialized;
  }

  /**
   * 是否正在处理
   */
  public get processing(): boolean {
    return this.isProcessing;
  }

  /**
   * 检查MediaPipe是否可用
   */
  public static isMediaPipeAvailable(): boolean {
    return RealMediaPipeFaceDetector.isAvailable();
  }

  /**
   * 获取支持的表情类型
   */
  public static getSupportedExpressions(): ExpressionType[] {
    return Object.values(ExpressionType);
  }

  /**
   * 销毁节点
   */
  public destroy(): void {
    if (this.faceDetector) {
      this.faceDetector.destroy();
      this.faceDetector = null;
    }

    this.isInitialized = false;
    this.isProcessing = false;
    this.lastResults = null;
    this.processingCount = 0;
    this.successCount = 0;
    this.averageProcessingTime = 0;

    super.destroy?.();
  }
}
