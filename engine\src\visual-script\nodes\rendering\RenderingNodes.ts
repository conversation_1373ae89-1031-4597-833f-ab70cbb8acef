/**
 * 渲染系统节点集合
 * 提供材质系统、光照控制、相机管理等渲染相关功能的节点
 */
import { VisualScriptNode } from '../../visualscript/VisualScriptNode';
import { Debug } from '../../utils/Debug';
import { 
  Material, 
  MeshBasicMaterial, 
  MeshStandardMaterial, 
  MeshPhysicalMaterial,
  Texture, 
  Color, 
  Vector3, 
  Camera, 
  PerspectiveCamera, 
  OrthographicCamera,
  Light,
  DirectionalLight,
  PointLight,
  SpotLight,
  AmbientLight,
  Scene,
  WebGLRenderer
} from 'three';

/**
 * 材质类型枚举
 */
export enum MaterialType {
  BASIC = 'basic',
  STANDARD = 'standard',
  PHYSICAL = 'physical',
  LAMBERT = 'lambert',
  PHONG = 'phong',
  TOON = 'toon',
  SHADER = 'shader',
  CUSTOM = 'custom'
}

/**
 * 光照类型枚举
 */
export enum LightType {
  DIRECTIONAL = 'directional',
  POINT = 'point',
  SPOT = 'spot',
  AMBIENT = 'ambient',
  HEMISPHERE = 'hemisphere',
  AREA = 'area'
}

/**
 * 相机类型枚举
 */
export enum CameraType {
  PERSPECTIVE = 'perspective',
  ORTHOGRAPHIC = 'orthographic'
}

/**
 * 渲染质量枚举
 */
export enum RenderQuality {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  ULTRA = 'ultra'
}

/**
 * 材质配置接口
 */
export interface MaterialConfig {
  type: MaterialType;
  color: Color;
  opacity: number;
  transparent: boolean;
  wireframe: boolean;
  metalness?: number;
  roughness?: number;
  emissive?: Color;
  emissiveIntensity?: number;
  normalScale?: number;
  envMapIntensity?: number;
  clearcoat?: number;
  clearcoatRoughness?: number;
  transmission?: number;
  thickness?: number;
  ior?: number;
}

/**
 * 光照配置接口
 */
export interface LightConfig {
  type: LightType;
  color: Color;
  intensity: number;
  position?: Vector3;
  target?: Vector3;
  distance?: number;
  decay?: number;
  angle?: number;
  penumbra?: number;
  castShadow?: boolean;
  shadowMapSize?: number;
  shadowBias?: number;
  shadowRadius?: number;
}

/**
 * 相机配置接口
 */
export interface CameraConfig {
  type: CameraType;
  position: Vector3;
  target: Vector3;
  fov?: number;
  aspect?: number;
  near?: number;
  far?: number;
  left?: number;
  right?: number;
  top?: number;
  bottom?: number;
  zoom?: number;
}

/**
 * 渲染配置接口
 */
export interface RenderConfig {
  quality: RenderQuality;
  antialias: boolean;
  shadows: boolean;
  shadowType: 'basic' | 'pcf' | 'pcfsoft' | 'vsm';
  toneMapping: 'none' | 'linear' | 'reinhard' | 'cineon' | 'aces';
  toneMappingExposure: number;
  outputEncoding: 'linear' | 'srgb';
  physicallyCorrectLights: boolean;
  gammaFactor: number;
}

/**
 * 渲染管理器
 */
class RenderingManager {
  private materials: Map<string, Material> = new Map();
  private lights: Map<string, Light> = new Map();
  private cameras: Map<string, Camera> = new Map();
  private textures: Map<string, Texture> = new Map();
  private renderer: WebGLRenderer | null = null;
  private scene: Scene | null = null;
  private eventListeners: Map<string, Function[]> = new Map();

  /**
   * 设置渲染器
   */
  setRenderer(renderer: WebGLRenderer): void {
    this.renderer = renderer;
  }

  /**
   * 设置场景
   */
  setScene(scene: Scene): void {
    this.scene = scene;
  }

  /**
   * 创建材质
   */
  createMaterial(id: string, config: MaterialConfig): Material {
    let material: Material;

    switch (config.type) {
      case MaterialType.BASIC:
        material = new MeshBasicMaterial({
          color: config.color,
          opacity: config.opacity,
          transparent: config.transparent,
          wireframe: config.wireframe
        });
        break;

      case MaterialType.STANDARD:
        material = new MeshStandardMaterial({
          color: config.color,
          opacity: config.opacity,
          transparent: config.transparent,
          wireframe: config.wireframe,
          metalness: config.metalness || 0,
          roughness: config.roughness || 1,
          emissive: config.emissive || new Color(0x000000),
          emissiveIntensity: config.emissiveIntensity || 1,
          envMapIntensity: config.envMapIntensity || 1
        });
        break;

      case MaterialType.PHYSICAL:
        material = new MeshPhysicalMaterial({
          color: config.color,
          opacity: config.opacity,
          transparent: config.transparent,
          wireframe: config.wireframe,
          metalness: config.metalness || 0,
          roughness: config.roughness || 1,
          emissive: config.emissive || new Color(0x000000),
          emissiveIntensity: config.emissiveIntensity || 1,
          clearcoat: config.clearcoat || 0,
          clearcoatRoughness: config.clearcoatRoughness || 0,
          transmission: config.transmission || 0,
          thickness: config.thickness || 0,
          ior: config.ior || 1.5
        });
        break;

      default:
        material = new MeshStandardMaterial({
          color: config.color,
          opacity: config.opacity,
          transparent: config.transparent,
          wireframe: config.wireframe
        });
    }

    this.materials.set(id, material);
    this.emit('materialCreated', { id, material });

    Debug.log('RenderingManager', `材质创建: ${id} (${config.type})`);
    return material;
  }

  /**
   * 创建光源
   */
  createLight(id: string, config: LightConfig): Light {
    let light: Light;

    switch (config.type) {
      case LightType.DIRECTIONAL:
        light = new DirectionalLight(config.color, config.intensity);
        if (config.position) {
          light.position.copy(config.position);
        }
        if (config.target && light instanceof DirectionalLight) {
          light.target.position.copy(config.target);
        }
        break;

      case LightType.POINT:
        light = new PointLight(config.color, config.intensity, config.distance, config.decay);
        if (config.position) {
          light.position.copy(config.position);
        }
        break;

      case LightType.SPOT:
        light = new SpotLight(config.color, config.intensity, config.distance, config.angle, config.penumbra, config.decay);
        if (config.position) {
          light.position.copy(config.position);
        }
        if (config.target && light instanceof SpotLight) {
          light.target.position.copy(config.target);
        }
        break;

      case LightType.AMBIENT:
        light = new AmbientLight(config.color, config.intensity);
        break;

      default:
        light = new DirectionalLight(config.color, config.intensity);
    }

    // 设置阴影
    if (config.castShadow && light instanceof DirectionalLight || light instanceof PointLight || light instanceof SpotLight) {
      light.castShadow = true;
      if (config.shadowMapSize) {
        light.shadow.mapSize.setScalar(config.shadowMapSize);
      }
      if (config.shadowBias !== undefined) {
        light.shadow.bias = config.shadowBias;
      }
      if (config.shadowRadius !== undefined) {
        light.shadow.radius = config.shadowRadius;
      }
    }

    this.lights.set(id, light);
    
    // 添加到场景
    if (this.scene) {
      this.scene.add(light);
      if (light instanceof DirectionalLight || light instanceof SpotLight) {
        this.scene.add(light.target);
      }
    }

    this.emit('lightCreated', { id, light });

    Debug.log('RenderingManager', `光源创建: ${id} (${config.type})`);
    return light;
  }

  /**
   * 创建相机
   */
  createCamera(id: string, config: CameraConfig): Camera {
    let camera: Camera;

    switch (config.type) {
      case CameraType.PERSPECTIVE:
        camera = new PerspectiveCamera(
          config.fov || 75,
          config.aspect || 1,
          config.near || 0.1,
          config.far || 1000
        );
        break;

      case CameraType.ORTHOGRAPHIC:
        camera = new OrthographicCamera(
          config.left || -1,
          config.right || 1,
          config.top || 1,
          config.bottom || -1,
          config.near || 0.1,
          config.far || 1000
        );
        break;

      default:
        camera = new PerspectiveCamera(75, 1, 0.1, 1000);
    }

    camera.position.copy(config.position);
    camera.lookAt(config.target);

    if (config.zoom && camera instanceof OrthographicCamera) {
      camera.zoom = config.zoom;
      camera.updateProjectionMatrix();
    }

    this.cameras.set(id, camera);
    this.emit('cameraCreated', { id, camera });

    Debug.log('RenderingManager', `相机创建: ${id} (${config.type})`);
    return camera;
  }

  /**
   * 配置渲染器
   */
  configureRenderer(config: RenderConfig): void {
    if (!this.renderer) {
      Debug.warn('RenderingManager', '渲染器未设置');
      return;
    }

    // 设置抗锯齿
    // 注意：抗锯齿需要在创建渲染器时设置，这里只是记录配置

    // 设置阴影
    this.renderer.shadowMap.enabled = config.shadows;
    switch (config.shadowType) {
      case 'basic':
        this.renderer.shadowMap.type = 0; // BasicShadowMap
        break;
      case 'pcf':
        this.renderer.shadowMap.type = 1; // PCFShadowMap
        break;
      case 'pcfsoft':
        this.renderer.shadowMap.type = 2; // PCFSoftShadowMap
        break;
      case 'vsm':
        this.renderer.shadowMap.type = 3; // VSMShadowMap
        break;
    }

    // 设置色调映射
    switch (config.toneMapping) {
      case 'none':
        this.renderer.toneMapping = 0; // NoToneMapping
        break;
      case 'linear':
        this.renderer.toneMapping = 1; // LinearToneMapping
        break;
      case 'reinhard':
        this.renderer.toneMapping = 2; // ReinhardToneMapping
        break;
      case 'cineon':
        this.renderer.toneMapping = 3; // CineonToneMapping
        break;
      case 'aces':
        this.renderer.toneMapping = 4; // ACESFilmicToneMapping
        break;
    }

    this.renderer.toneMappingExposure = config.toneMappingExposure;

    // 设置输出编码
    switch (config.outputEncoding) {
      case 'linear':
        this.renderer.outputEncoding = 3000; // LinearEncoding
        break;
      case 'srgb':
        this.renderer.outputEncoding = 3001; // sRGBEncoding
        break;
    }

    // 设置物理正确光照
    this.renderer.physicallyCorrectLights = config.physicallyCorrectLights;

    this.emit('rendererConfigured', { config });

    Debug.log('RenderingManager', `渲染器配置完成: ${config.quality}`);
  }

  /**
   * 获取材质
   */
  getMaterial(id: string): Material | undefined {
    return this.materials.get(id);
  }

  /**
   * 获取光源
   */
  getLight(id: string): Light | undefined {
    return this.lights.get(id);
  }

  /**
   * 获取相机
   */
  getCamera(id: string): Camera | undefined {
    return this.cameras.get(id);
  }

  /**
   * 更新材质属性
   */
  updateMaterial(id: string, properties: Partial<MaterialConfig>): boolean {
    const material = this.materials.get(id);
    if (!material) {
      return false;
    }

    // 更新通用属性
    if (properties.color) {
      (material as any).color = properties.color;
    }
    if (properties.opacity !== undefined) {
      material.opacity = properties.opacity;
    }
    if (properties.transparent !== undefined) {
      material.transparent = properties.transparent;
    }
    if (properties.wireframe !== undefined) {
      (material as any).wireframe = properties.wireframe;
    }

    // 更新PBR属性
    if (material instanceof MeshStandardMaterial || material instanceof MeshPhysicalMaterial) {
      if (properties.metalness !== undefined) {
        material.metalness = properties.metalness;
      }
      if (properties.roughness !== undefined) {
        material.roughness = properties.roughness;
      }
      if (properties.emissive) {
        material.emissive = properties.emissive;
      }
      if (properties.emissiveIntensity !== undefined) {
        material.emissiveIntensity = properties.emissiveIntensity;
      }
    }

    material.needsUpdate = true;
    this.emit('materialUpdated', { id, material, properties });

    Debug.log('RenderingManager', `材质更新: ${id}`);
    return true;
  }

  // 事件系统
  on(event: string, callback: Function): void {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, []);
    }
    this.eventListeners.get(event)!.push(callback);
  }

  off(event: string, callback: Function): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      const index = listeners.indexOf(callback);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    }
  }

  private emit(event: string, data?: any): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      listeners.forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          Debug.error('RenderingManager', `事件回调执行失败: ${event}`, error);
        }
      });
    }
  }

  /**
   * 清理资源
   */
  cleanup(): void {
    // 清理材质
    for (const material of this.materials.values()) {
      material.dispose();
    }
    this.materials.clear();

    // 清理光源
    for (const light of this.lights.values()) {
      if (this.scene) {
        this.scene.remove(light);
      }
    }
    this.lights.clear();

    // 清理相机
    this.cameras.clear();

    // 清理纹理
    for (const texture of this.textures.values()) {
      texture.dispose();
    }
    this.textures.clear();

    this.eventListeners.clear();
  }
}

/**
 * 材质系统节点
 */
export class MaterialSystemNode extends VisualScriptNode {
  public static readonly TYPE = 'MaterialSystem';
  public static readonly NAME = '材质系统';
  public static readonly DESCRIPTION = '创建和管理材质';

  private static renderingManager: RenderingManager = new RenderingManager();

  constructor(nodeType: string = MaterialSystemNode.TYPE, name: string = MaterialSystemNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('create', 'trigger', '创建材质');
    this.addInput('update', 'trigger', '更新材质');
    this.addInput('materialId', 'string', '材质ID');
    this.addInput('materialType', 'string', '材质类型');
    this.addInput('color', 'object', '颜色');
    this.addInput('opacity', 'number', '透明度');
    this.addInput('transparent', 'boolean', '透明');
    this.addInput('wireframe', 'boolean', '线框模式');
    this.addInput('metalness', 'number', '金属度');
    this.addInput('roughness', 'number', '粗糙度');
    this.addInput('emissive', 'object', '自发光颜色');
    this.addInput('emissiveIntensity', 'number', '自发光强度');

    // 输出端口
    this.addOutput('material', 'object', '材质对象');
    this.addOutput('materialId', 'string', '材质ID');
    this.addOutput('materialType', 'string', '材质类型');
    this.addOutput('onCreated', 'trigger', '材质创建完成');
    this.addOutput('onUpdated', 'trigger', '材质更新完成');
    this.addOutput('onError', 'trigger', '操作失败');
  }

  public execute(inputs?: any): any {
    try {
      const createTrigger = inputs?.create;
      const updateTrigger = inputs?.update;

      if (createTrigger) {
        return this.createMaterial(inputs);
      } else if (updateTrigger) {
        return this.updateMaterial(inputs);
      }

      return this.getDefaultOutputs();

    } catch (error) {
      Debug.error('MaterialSystemNode', '材质操作失败', error);
      return {
        ...this.getDefaultOutputs(),
        onError: true
      };
    }
  }

  private createMaterial(inputs: any): any {
    const materialId = inputs?.materialId as string || this.generateMaterialId();
    const materialType = inputs?.materialType as string || 'standard';
    const color = inputs?.color as Color || new Color(0xffffff);
    const opacity = inputs?.opacity as number ?? 1.0;
    const transparent = inputs?.transparent as boolean ?? false;
    const wireframe = inputs?.wireframe as boolean ?? false;
    const metalness = inputs?.metalness as number ?? 0.0;
    const roughness = inputs?.roughness as number ?? 1.0;
    const emissive = inputs?.emissive as Color || new Color(0x000000);
    const emissiveIntensity = inputs?.emissiveIntensity as number ?? 1.0;

    const config: MaterialConfig = {
      type: materialType as MaterialType,
      color,
      opacity,
      transparent,
      wireframe,
      metalness,
      roughness,
      emissive,
      emissiveIntensity
    };

    const material = MaterialSystemNode.renderingManager.createMaterial(materialId, config);

    Debug.log('MaterialSystemNode', `材质创建成功: ${materialId} (${materialType})`);

    return {
      material,
      materialId,
      materialType,
      onCreated: true,
      onUpdated: false,
      onError: false
    };
  }

  private updateMaterial(inputs: any): any {
    const materialId = inputs?.materialId as string;

    if (!materialId) {
      throw new Error('未提供材质ID');
    }

    const properties: Partial<MaterialConfig> = {};

    if (inputs?.color) properties.color = inputs.color;
    if (inputs?.opacity !== undefined) properties.opacity = inputs.opacity;
    if (inputs?.transparent !== undefined) properties.transparent = inputs.transparent;
    if (inputs?.wireframe !== undefined) properties.wireframe = inputs.wireframe;
    if (inputs?.metalness !== undefined) properties.metalness = inputs.metalness;
    if (inputs?.roughness !== undefined) properties.roughness = inputs.roughness;
    if (inputs?.emissive) properties.emissive = inputs.emissive;
    if (inputs?.emissiveIntensity !== undefined) properties.emissiveIntensity = inputs.emissiveIntensity;

    const success = MaterialSystemNode.renderingManager.updateMaterial(materialId, properties);

    if (!success) {
      throw new Error('材质更新失败');
    }

    const material = MaterialSystemNode.renderingManager.getMaterial(materialId);

    Debug.log('MaterialSystemNode', `材质更新成功: ${materialId}`);

    return {
      material,
      materialId,
      materialType: (material as any)?.type || 'unknown',
      onCreated: false,
      onUpdated: true,
      onError: false
    };
  }

  private generateMaterialId(): string {
    return 'mat_' + Math.random().toString(36).substr(2, 9);
  }

  private getDefaultOutputs(): any {
    return {
      material: null,
      materialId: '',
      materialType: '',
      onCreated: false,
      onUpdated: false,
      onError: false
    };
  }
}

/**
 * 光照控制节点
 */
export class LightControlNode extends VisualScriptNode {
  public static readonly TYPE = 'LightControl';
  public static readonly NAME = '光照控制';
  public static readonly DESCRIPTION = '创建和管理光源';

  private static renderingManager: RenderingManager = new RenderingManager();

  constructor(nodeType: string = LightControlNode.TYPE, name: string = LightControlNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('create', 'trigger', '创建光源');
    this.addInput('update', 'trigger', '更新光源');
    this.addInput('lightId', 'string', '光源ID');
    this.addInput('lightType', 'string', '光源类型');
    this.addInput('color', 'object', '光源颜色');
    this.addInput('intensity', 'number', '光源强度');
    this.addInput('position', 'object', '光源位置');
    this.addInput('target', 'object', '目标位置');
    this.addInput('distance', 'number', '光照距离');
    this.addInput('angle', 'number', '光照角度');
    this.addInput('penumbra', 'number', '半影');
    this.addInput('castShadow', 'boolean', '投射阴影');

    // 输出端口
    this.addOutput('light', 'object', '光源对象');
    this.addOutput('lightId', 'string', '光源ID');
    this.addOutput('lightType', 'string', '光源类型');
    this.addOutput('onCreated', 'trigger', '光源创建完成');
    this.addOutput('onUpdated', 'trigger', '光源更新完成');
    this.addOutput('onError', 'trigger', '操作失败');
  }

  public execute(inputs?: any): any {
    try {
      const createTrigger = inputs?.create;
      const updateTrigger = inputs?.update;

      if (createTrigger) {
        return this.createLight(inputs);
      } else if (updateTrigger) {
        return this.updateLight(inputs);
      }

      return this.getDefaultOutputs();

    } catch (error) {
      Debug.error('LightControlNode', '光照操作失败', error);
      return {
        ...this.getDefaultOutputs(),
        onError: true
      };
    }
  }

  private createLight(inputs: any): any {
    const lightId = inputs?.lightId as string || this.generateLightId();
    const lightType = inputs?.lightType as string || 'directional';
    const color = inputs?.color as Color || new Color(0xffffff);
    const intensity = inputs?.intensity as number ?? 1.0;
    const position = inputs?.position as Vector3 || new Vector3(0, 10, 0);
    const target = inputs?.target as Vector3 || new Vector3(0, 0, 0);
    const distance = inputs?.distance as number ?? 0;
    const angle = inputs?.angle as number ?? Math.PI / 3;
    const penumbra = inputs?.penumbra as number ?? 0;
    const castShadow = inputs?.castShadow as boolean ?? false;

    const config: LightConfig = {
      type: lightType as LightType,
      color,
      intensity,
      position,
      target,
      distance,
      angle,
      penumbra,
      castShadow,
      shadowMapSize: 1024,
      shadowBias: -0.0001,
      shadowRadius: 1
    };

    const light = LightControlNode.renderingManager.createLight(lightId, config);

    Debug.log('LightControlNode', `光源创建成功: ${lightId} (${lightType})`);

    return {
      light,
      lightId,
      lightType,
      onCreated: true,
      onUpdated: false,
      onError: false
    };
  }

  private updateLight(inputs: any): any {
    const lightId = inputs?.lightId as string;

    if (!lightId) {
      throw new Error('未提供光源ID');
    }

    const light = LightControlNode.renderingManager.getLight(lightId);

    if (!light) {
      throw new Error('光源不存在');
    }

    // 更新光源属性
    if (inputs?.color) {
      light.color = inputs.color;
    }
    if (inputs?.intensity !== undefined) {
      light.intensity = inputs.intensity;
    }
    if (inputs?.position) {
      light.position.copy(inputs.position);
    }
    if (inputs?.target && (light instanceof DirectionalLight || light instanceof SpotLight)) {
      light.target.position.copy(inputs.target);
    }
    if (inputs?.distance !== undefined && (light instanceof PointLight || light instanceof SpotLight)) {
      (light as PointLight | SpotLight).distance = inputs.distance;
    }
    if (inputs?.angle !== undefined && light instanceof SpotLight) {
      light.angle = inputs.angle;
    }
    if (inputs?.penumbra !== undefined && light instanceof SpotLight) {
      light.penumbra = inputs.penumbra;
    }
    if (inputs?.castShadow !== undefined) {
      light.castShadow = inputs.castShadow;
    }

    Debug.log('LightControlNode', `光源更新成功: ${lightId}`);

    return {
      light,
      lightId,
      lightType: light.type,
      onCreated: false,
      onUpdated: true,
      onError: false
    };
  }

  private generateLightId(): string {
    return 'light_' + Math.random().toString(36).substr(2, 9);
  }

  private getDefaultOutputs(): any {
    return {
      light: null,
      lightId: '',
      lightType: '',
      onCreated: false,
      onUpdated: false,
      onError: false
    };
  }
}

/**
 * 相机管理节点
 */
export class CameraManagerNode extends VisualScriptNode {
  public static readonly TYPE = 'CameraManager';
  public static readonly NAME = '相机管理';
  public static readonly DESCRIPTION = '创建和管理相机';

  private static renderingManager: RenderingManager = new RenderingManager();

  constructor(nodeType: string = CameraManagerNode.TYPE, name: string = CameraManagerNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('create', 'trigger', '创建相机');
    this.addInput('update', 'trigger', '更新相机');
    this.addInput('lookAt', 'trigger', '看向目标');
    this.addInput('cameraId', 'string', '相机ID');
    this.addInput('cameraType', 'string', '相机类型');
    this.addInput('position', 'object', '相机位置');
    this.addInput('target', 'object', '目标位置');
    this.addInput('fov', 'number', '视野角度');
    this.addInput('aspect', 'number', '宽高比');
    this.addInput('near', 'number', '近裁剪面');
    this.addInput('far', 'number', '远裁剪面');
    this.addInput('zoom', 'number', '缩放');

    // 输出端口
    this.addOutput('camera', 'object', '相机对象');
    this.addOutput('cameraId', 'string', '相机ID');
    this.addOutput('cameraType', 'string', '相机类型');
    this.addOutput('position', 'object', '相机位置');
    this.addOutput('target', 'object', '目标位置');
    this.addOutput('onCreated', 'trigger', '相机创建完成');
    this.addOutput('onUpdated', 'trigger', '相机更新完成');
    this.addOutput('onError', 'trigger', '操作失败');
  }

  public execute(inputs?: any): any {
    try {
      const createTrigger = inputs?.create;
      const updateTrigger = inputs?.update;
      const lookAtTrigger = inputs?.lookAt;

      if (createTrigger) {
        return this.createCamera(inputs);
      } else if (updateTrigger) {
        return this.updateCamera(inputs);
      } else if (lookAtTrigger) {
        return this.lookAtTarget(inputs);
      }

      return this.getDefaultOutputs();

    } catch (error) {
      Debug.error('CameraManagerNode', '相机操作失败', error);
      return {
        ...this.getDefaultOutputs(),
        onError: true
      };
    }
  }

  private createCamera(inputs: any): any {
    const cameraId = inputs?.cameraId as string || this.generateCameraId();
    const cameraType = inputs?.cameraType as string || 'perspective';
    const position = inputs?.position as Vector3 || new Vector3(0, 0, 5);
    const target = inputs?.target as Vector3 || new Vector3(0, 0, 0);
    const fov = inputs?.fov as number ?? 75;
    const aspect = inputs?.aspect as number ?? 1;
    const near = inputs?.near as number ?? 0.1;
    const far = inputs?.far as number ?? 1000;
    const zoom = inputs?.zoom as number ?? 1;

    const config: CameraConfig = {
      type: cameraType as CameraType,
      position,
      target,
      fov,
      aspect,
      near,
      far,
      zoom
    };

    const camera = CameraManagerNode.renderingManager.createCamera(cameraId, config);

    Debug.log('CameraManagerNode', `相机创建成功: ${cameraId} (${cameraType})`);

    return {
      camera,
      cameraId,
      cameraType,
      position: camera.position,
      target,
      onCreated: true,
      onUpdated: false,
      onError: false
    };
  }

  private updateCamera(inputs: any): any {
    const cameraId = inputs?.cameraId as string;

    if (!cameraId) {
      throw new Error('未提供相机ID');
    }

    const camera = CameraManagerNode.renderingManager.getCamera(cameraId);

    if (!camera) {
      throw new Error('相机不存在');
    }

    // 更新相机属性
    if (inputs?.position) {
      camera.position.copy(inputs.position);
    }

    if (camera instanceof PerspectiveCamera) {
      if (inputs?.fov !== undefined) {
        camera.fov = inputs.fov;
        camera.updateProjectionMatrix();
      }
      if (inputs?.aspect !== undefined) {
        camera.aspect = inputs.aspect;
        camera.updateProjectionMatrix();
      }
    }

    if (camera instanceof OrthographicCamera) {
      if (inputs?.zoom !== undefined) {
        camera.zoom = inputs.zoom;
        camera.updateProjectionMatrix();
      }
    }

    if (inputs?.near !== undefined) {
      camera.near = inputs.near;
      camera.updateProjectionMatrix();
    }
    if (inputs?.far !== undefined) {
      camera.far = inputs.far;
      camera.updateProjectionMatrix();
    }

    Debug.log('CameraManagerNode', `相机更新成功: ${cameraId}`);

    return {
      camera,
      cameraId,
      cameraType: camera.type,
      position: camera.position,
      target: inputs?.target || new Vector3(0, 0, 0),
      onCreated: false,
      onUpdated: true,
      onError: false
    };
  }

  private lookAtTarget(inputs: any): any {
    const cameraId = inputs?.cameraId as string;
    const target = inputs?.target as Vector3;

    if (!cameraId) {
      throw new Error('未提供相机ID');
    }

    if (!target) {
      throw new Error('未提供目标位置');
    }

    const camera = CameraManagerNode.renderingManager.getCamera(cameraId);

    if (!camera) {
      throw new Error('相机不存在');
    }

    camera.lookAt(target);

    Debug.log('CameraManagerNode', `相机朝向更新: ${cameraId}`);

    return {
      camera,
      cameraId,
      cameraType: camera.type,
      position: camera.position,
      target,
      onCreated: false,
      onUpdated: true,
      onError: false
    };
  }

  private generateCameraId(): string {
    return 'cam_' + Math.random().toString(36).substr(2, 9);
  }

  private getDefaultOutputs(): any {
    return {
      camera: null,
      cameraId: '',
      cameraType: '',
      position: null,
      target: null,
      onCreated: false,
      onUpdated: false,
      onError: false
    };
  }
}

/**
 * 渲染配置节点
 */
export class RenderConfigNode extends VisualScriptNode {
  public static readonly TYPE = 'RenderConfig';
  public static readonly NAME = '渲染配置';
  public static readonly DESCRIPTION = '配置渲染器设置';

  private static renderingManager: RenderingManager = new RenderingManager();

  constructor(nodeType: string = RenderConfigNode.TYPE, name: string = RenderConfigNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('configure', 'trigger', '应用配置');
    this.addInput('quality', 'string', '渲染质量');
    this.addInput('antialias', 'boolean', '抗锯齿');
    this.addInput('shadows', 'boolean', '阴影');
    this.addInput('shadowType', 'string', '阴影类型');
    this.addInput('toneMapping', 'string', '色调映射');
    this.addInput('toneMappingExposure', 'number', '曝光度');
    this.addInput('outputEncoding', 'string', '输出编码');
    this.addInput('physicallyCorrectLights', 'boolean', '物理正确光照');

    // 输出端口
    this.addOutput('config', 'object', '渲染配置');
    this.addOutput('quality', 'string', '渲染质量');
    this.addOutput('onConfigured', 'trigger', '配置完成');
    this.addOutput('onError', 'trigger', '配置失败');
  }

  public execute(inputs?: any): any {
    try {
      const configureTrigger = inputs?.configure;
      if (!configureTrigger) {
        return this.getDefaultOutputs();
      }

      const quality = inputs?.quality as string || 'medium';
      const antialias = inputs?.antialias as boolean ?? true;
      const shadows = inputs?.shadows as boolean ?? true;
      const shadowType = inputs?.shadowType as string || 'pcf';
      const toneMapping = inputs?.toneMapping as string || 'aces';
      const toneMappingExposure = inputs?.toneMappingExposure as number ?? 1.0;
      const outputEncoding = inputs?.outputEncoding as string || 'srgb';
      const physicallyCorrectLights = inputs?.physicallyCorrectLights as boolean ?? true;

      const config: RenderConfig = {
        quality: quality as RenderQuality,
        antialias,
        shadows,
        shadowType: shadowType as any,
        toneMapping: toneMapping as any,
        toneMappingExposure,
        outputEncoding: outputEncoding as any,
        physicallyCorrectLights,
        gammaFactor: 2.2
      };

      RenderConfigNode.renderingManager.configureRenderer(config);

      Debug.log('RenderConfigNode', `渲染配置应用成功: ${quality}`);

      return {
        config,
        quality,
        onConfigured: true,
        onError: false
      };

    } catch (error) {
      Debug.error('RenderConfigNode', '渲染配置失败', error);
      return {
        config: null,
        quality: '',
        onConfigured: false,
        onError: true
      };
    }
  }

  private getDefaultOutputs(): any {
    return {
      config: null,
      quality: '',
      onConfigured: false,
      onError: false
    };
  }
}
