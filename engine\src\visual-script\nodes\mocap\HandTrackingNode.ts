/**
 * 手部追踪节点
 * 使用MediaPipe进行手部关键点检测和手势识别，支持复杂手势算法
 */
import { VisualScriptNode } from '../../../visualscript/VisualScriptNode';
import { Debug } from '../../../utils/Debug';
import { Vector3 } from 'three';

/**
 * 关键点数据接口
 */
export interface LandmarkData {
  x: number;
  y: number;
  z?: number;
  visibility?: number;
}

/**
 * 手势类型枚举
 */
export enum GestureType {
  UNKNOWN = 'unknown',
  OPEN_HAND = 'open_hand',
  FIST = 'fist',
  POINTING = 'pointing',
  THUMBS_UP = 'thumbs_up',
  THUMBS_DOWN = 'thumbs_down',
  PEACE = 'peace',
  OK = 'ok',
  GRAB = 'grab',
  PINCH = 'pinch',
  ROCK = 'rock',
  PAPER = 'paper',
  SCISSORS = 'scissors',
  CALL_ME = 'call_me',
  LOVE_YOU = 'love_you'
}

/**
 * 手势识别结果
 */
export interface GestureResult {
  type: GestureType;
  confidence: number;
  hand: 'left' | 'right';
  position: Vector3;
  timestamp: number;
  fingerStates: FingerState[];
  handOrientation: HandOrientation;
}

/**
 * 手指状态
 */
export interface FingerState {
  finger: 'thumb' | 'index' | 'middle' | 'ring' | 'pinky';
  extended: boolean;
  confidence: number;
  angle: number;
}

/**
 * 手部方向
 */
export interface HandOrientation {
  pitch: number;  // 俯仰角
  yaw: number;    // 偏航角
  roll: number;   // 翻滚角
}

/**
 * 手部检测结果
 */
export interface HandResults {
  leftHand?: LandmarkData[];
  rightHand?: LandmarkData[];
  handedness?: any[];
  confidence: number;
  processingTime: number;
  timestamp: number;
}

/**
 * MediaPipe手部配置
 */
export interface MediaPipeHandsConfig {
  maxNumHands: number;
  modelComplexity: number;
  minDetectionConfidence: number;
  minTrackingConfidence: number;
  staticImageMode: boolean;
}

/**
 * 高级手势识别器
 */
class AdvancedGestureRecognizer {
  private gestureHistory: Map<string, GestureResult[]> = new Map();
  private maxHistorySize = 10;
  private stabilityThreshold = 0.7;

  /**
   * MediaPipe手部关键点索引
   */
  private static readonly HAND_LANDMARKS = {
    WRIST: 0,
    THUMB_CMC: 1,
    THUMB_MCP: 2,
    THUMB_IP: 3,
    THUMB_TIP: 4,
    INDEX_FINGER_MCP: 5,
    INDEX_FINGER_PIP: 6,
    INDEX_FINGER_DIP: 7,
    INDEX_FINGER_TIP: 8,
    MIDDLE_FINGER_MCP: 9,
    MIDDLE_FINGER_PIP: 10,
    MIDDLE_FINGER_DIP: 11,
    MIDDLE_FINGER_TIP: 12,
    RING_FINGER_MCP: 13,
    RING_FINGER_PIP: 14,
    RING_FINGER_DIP: 15,
    RING_FINGER_TIP: 16,
    PINKY_MCP: 17,
    PINKY_PIP: 18,
    PINKY_DIP: 19,
    PINKY_TIP: 20
  };

  /**
   * 识别手势
   */
  recognizeGesture(landmarks: LandmarkData[], hand: 'left' | 'right'): GestureResult {
    const fingerStates = this.analyzeFingerStates(landmarks);
    const handOrientation = this.calculateHandOrientation(landmarks);
    const gestureType = this.classifyGesture(fingerStates, handOrientation);

    const wrist = landmarks[AdvancedGestureRecognizer.HAND_LANDMARKS.WRIST];
    const position = new Vector3(wrist.x, wrist.y, wrist.z || 0);

    const result: GestureResult = {
      type: gestureType.type,
      confidence: gestureType.confidence,
      hand,
      position,
      timestamp: Date.now(),
      fingerStates,
      handOrientation
    };

    // 应用稳定性滤波
    return this.applyStabilityFilter(result);
  }

  /**
   * 分析手指状态
   */
  private analyzeFingerStates(landmarks: LandmarkData[]): FingerState[] {
    const states: FingerState[] = [];
    const indices = AdvancedGestureRecognizer.HAND_LANDMARKS;

    // 拇指
    const thumbExtended = this.isThumbExtended(landmarks);
    states.push({
      finger: 'thumb',
      extended: thumbExtended.extended,
      confidence: thumbExtended.confidence,
      angle: thumbExtended.angle
    });

    // 其他手指
    const fingers = [
      { name: 'index' as const, mcp: indices.INDEX_FINGER_MCP, tip: indices.INDEX_FINGER_TIP },
      { name: 'middle' as const, mcp: indices.MIDDLE_FINGER_MCP, tip: indices.MIDDLE_FINGER_TIP },
      { name: 'ring' as const, mcp: indices.RING_FINGER_MCP, tip: indices.RING_FINGER_TIP },
      { name: 'pinky' as const, mcp: indices.PINKY_MCP, tip: indices.PINKY_TIP }
    ];

    for (const finger of fingers) {
      const extended = this.isFingerExtended(landmarks, finger.mcp, finger.tip);
      states.push({
        finger: finger.name,
        extended: extended.extended,
        confidence: extended.confidence,
        angle: extended.angle
      });
    }

    return states;
  }

  /**
   * 判断拇指是否伸展
   */
  private isThumbExtended(landmarks: LandmarkData[]): { extended: boolean; confidence: number; angle: number } {
    const indices = AdvancedGestureRecognizer.HAND_LANDMARKS;
    const cmc = landmarks[indices.THUMB_CMC];
    const mcp = landmarks[indices.THUMB_MCP];
    const ip = landmarks[indices.THUMB_IP];
    const tip = landmarks[indices.THUMB_TIP];

    // 计算拇指的角度
    const angle1 = this.calculateAngle(cmc, mcp, ip);
    const angle2 = this.calculateAngle(mcp, ip, tip);

    // 拇指伸展的判断条件
    const extended = angle1 > 140 && angle2 > 140;
    const confidence = Math.min(angle1, angle2) / 180;

    return { extended, confidence, angle: (angle1 + angle2) / 2 };
  }

  /**
   * 判断手指是否伸展
   */
  private isFingerExtended(landmarks: LandmarkData[], mcpIndex: number, tipIndex: number): { extended: boolean; confidence: number; angle: number } {
    const mcp = landmarks[mcpIndex];
    const tip = landmarks[tipIndex];
    const wrist = landmarks[AdvancedGestureRecognizer.HAND_LANDMARKS.WRIST];

    // 计算手指相对于手腕的高度
    const fingerHeight = tip.y - wrist.y;
    const mcpHeight = mcp.y - wrist.y;

    // 伸展判断：指尖应该高于关节
    const extended = fingerHeight < mcpHeight - 0.02; // 负值表示向上
    const confidence = Math.abs(fingerHeight - mcpHeight) * 10;

    const angle = Math.atan2(tip.y - mcp.y, tip.x - mcp.x) * 180 / Math.PI;

    return { extended, confidence: Math.min(confidence, 1), angle };
  }

  /**
   * 计算手部方向
   */
  private calculateHandOrientation(landmarks: LandmarkData[]): HandOrientation {
    const indices = AdvancedGestureRecognizer.HAND_LANDMARKS;
    const wrist = landmarks[indices.WRIST];
    const middleMcp = landmarks[indices.MIDDLE_FINGER_MCP];
    const indexMcp = landmarks[indices.INDEX_FINGER_MCP];
    const pinkyMcp = landmarks[indices.PINKY_MCP];

    // 计算手掌平面的法向量
    const v1 = { x: middleMcp.x - wrist.x, y: middleMcp.y - wrist.y, z: (middleMcp.z || 0) - (wrist.z || 0) };
    const v2 = { x: indexMcp.x - pinkyMcp.x, y: indexMcp.y - pinkyMcp.y, z: (indexMcp.z || 0) - (pinkyMcp.z || 0) };

    // 计算欧拉角
    const pitch = Math.atan2(v1.y, Math.sqrt(v1.x * v1.x + v1.z * v1.z)) * 180 / Math.PI;
    const yaw = Math.atan2(v1.x, v1.z) * 180 / Math.PI;
    const roll = Math.atan2(v2.y, v2.x) * 180 / Math.PI;

    return { pitch, yaw, roll };
  }

  /**
   * 分类手势
   */
  private classifyGesture(fingerStates: FingerState[], orientation: HandOrientation): { type: GestureType; confidence: number } {
    const extendedFingers = fingerStates.filter(f => f.extended);
    const extendedCount = extendedFingers.length;

    // 基于伸展手指数量的基础分类
    switch (extendedCount) {
      case 0:
        return { type: GestureType.FIST, confidence: 0.9 };

      case 1:
        if (fingerStates[1].extended) { // index finger
          return { type: GestureType.POINTING, confidence: 0.85 };
        } else if (fingerStates[0].extended) { // thumb
          return orientation.pitch > 45 ?
            { type: GestureType.THUMBS_UP, confidence: 0.8 } :
            { type: GestureType.THUMBS_DOWN, confidence: 0.8 };
        }
        break;

      case 2:
        if (fingerStates[1].extended && fingerStates[2].extended) { // index + middle
          return { type: GestureType.PEACE, confidence: 0.8 };
        } else if (fingerStates[0].extended && fingerStates[1].extended) { // thumb + index
          return { type: GestureType.PINCH, confidence: 0.75 };
        }
        break;

      case 3:
        if (fingerStates[0].extended && fingerStates[1].extended && fingerStates[4].extended) { // thumb + index + pinky
          return { type: GestureType.LOVE_YOU, confidence: 0.8 };
        } else if (fingerStates[0].extended && fingerStates[1].extended && fingerStates[2].extended) {
          return { type: GestureType.OK, confidence: 0.75 };
        }
        break;

      case 5:
        return { type: GestureType.OPEN_HAND, confidence: 0.9 };
    }

    return { type: GestureType.UNKNOWN, confidence: 0.5 };
  }

  /**
   * 应用稳定性滤波
   */
  private applyStabilityFilter(result: GestureResult): GestureResult {
    const handKey = result.hand;

    if (!this.gestureHistory.has(handKey)) {
      this.gestureHistory.set(handKey, []);
    }

    const history = this.gestureHistory.get(handKey)!;
    history.push(result);

    if (history.length > this.maxHistorySize) {
      history.shift();
    }

    // 如果历史记录不足，直接返回
    if (history.length < 3) {
      return result;
    }

    // 计算最近几个手势的一致性
    const recentGestures = history.slice(-3);
    const gestureTypes = recentGestures.map(g => g.type);
    const mostCommon = this.getMostCommonGesture(gestureTypes);

    // 如果一致性高，使用稳定的手势
    if (mostCommon.count >= 2) {
      return {
        ...result,
        type: mostCommon.gesture,
        confidence: Math.min(result.confidence + 0.1, 1.0)
      };
    }

    return result;
  }

  /**
   * 获取最常见的手势
   */
  private getMostCommonGesture(gestures: GestureType[]): { gesture: GestureType; count: number } {
    const counts = new Map<GestureType, number>();

    for (const gesture of gestures) {
      counts.set(gesture, (counts.get(gesture) || 0) + 1);
    }

    let maxCount = 0;
    let mostCommon = GestureType.UNKNOWN;

    for (const [gesture, count] of counts) {
      if (count > maxCount) {
        maxCount = count;
        mostCommon = gesture;
      }
    }

    return { gesture: mostCommon, count: maxCount };
  }

  /**
   * 计算角度
   */
  private calculateAngle(p1: LandmarkData, p2: LandmarkData, p3: LandmarkData): number {
    const v1 = { x: p1.x - p2.x, y: p1.y - p2.y };
    const v2 = { x: p3.x - p2.x, y: p3.y - p2.y };

    const dot = v1.x * v2.x + v1.y * v2.y;
    const mag1 = Math.sqrt(v1.x * v1.x + v1.y * v1.y);
    const mag2 = Math.sqrt(v2.x * v2.x + v2.y * v2.y);

    if (mag1 === 0 || mag2 === 0) return 0;

    const cosAngle = dot / (mag1 * mag2);
    return Math.acos(Math.max(-1, Math.min(1, cosAngle))) * (180 / Math.PI);
  }

  /**
   * 重置历史记录
   */
  reset(): void {
    this.gestureHistory.clear();
  }
}

/**
 * 手部追踪节点配置
 */
export interface HandTrackingNodeConfig {
  /** 最大手部数量 */
  maxNumHands: number;
  /** 模型复杂度 (0-1) */
  modelComplexity: number;
  /** 最小检测置信度 */
  minDetectionConfidence: number;
  /** 最小跟踪置信度 */
  minTrackingConfidence: number;
  /** 是否启用手势识别 */
  enableGestureRecognition: boolean;
  /** 手势置信度阈值 */
  gestureConfidenceThreshold: number;
  /** 是否自动初始化 */
  autoInitialize: boolean;
  /** 是否启用静态图像模式 */
  staticImageMode: boolean;
  /** 是否启用双手协作检测 */
  enableBimanualGestures: boolean;
}

/**
 * 真实的MediaPipe手部检测器
 */
class RealMediaPipeHandsDetector {
  private hands: any = null;
  private config: MediaPipeHandsConfig;
  private isInitialized = false;
  private eventListeners: Map<string, Function[]> = new Map();
  private canvas: HTMLCanvasElement;
  private context: CanvasRenderingContext2D;

  constructor(config: MediaPipeHandsConfig) {
    this.config = config;

    // 创建离屏画布用于处理
    this.canvas = document.createElement('canvas');
    this.context = this.canvas.getContext('2d')!;
  }

  /**
   * 初始化MediaPipe Hands
   */
  async initialize(): Promise<void> {
    try {
      // 检查MediaPipe是否可用
      if (typeof window === 'undefined' || !(window as any).Hands) {
        throw new Error('MediaPipe Hands库未加载，请确保已引入MediaPipe脚本');
      }

      const Hands = (window as any).Hands;

      // 创建Hands实例
      this.hands = new Hands({
        locateFile: (file: string) => {
          return `https://cdn.jsdelivr.net/npm/@mediapipe/hands/${file}`;
        }
      });

      // 配置选项
      await this.hands.setOptions({
        maxNumHands: this.config.maxNumHands,
        modelComplexity: this.config.modelComplexity,
        minDetectionConfidence: this.config.minDetectionConfidence,
        minTrackingConfidence: this.config.minTrackingConfidence,
        staticImageMode: this.config.staticImageMode
      });

      // 设置结果回调
      this.hands.onResults((results: any) => {
        this.handleResults(results);
      });

      this.isInitialized = true;
      this.emit('initialized');

      Debug.log('MediaPipeHandsDetector', 'MediaPipe手部检测器初始化成功');

    } catch (error) {
      Debug.error('MediaPipeHandsDetector', 'MediaPipe初始化失败', error);
      this.isInitialized = false;
      this.emit('error', error);
      throw error;
    }
  }

  /**
   * 处理MediaPipe结果
   */
  private handleResults(results: any): void {
    const startTime = performance.now();

    try {
      const handResults: HandResults = {
        leftHand: undefined,
        rightHand: undefined,
        handedness: results.multiHandedness,
        confidence: 0,
        processingTime: 0,
        timestamp: Date.now()
      };

      // 处理检测到的手部
      if (results.multiHandLandmarks && results.multiHandedness) {
        for (let i = 0; i < results.multiHandLandmarks.length; i++) {
          const landmarks = results.multiHandLandmarks[i];
          const handedness = results.multiHandedness[i];

          const handLandmarks = landmarks.map((landmark: any) => ({
            x: landmark.x,
            y: landmark.y,
            z: landmark.z,
            visibility: landmark.visibility || 1.0
          }));

          // 计算置信度
          const confidence = handedness.score || 0;
          handResults.confidence = Math.max(handResults.confidence, confidence);

          // 根据手部类型分配
          if (handedness.label === 'Left') {
            handResults.leftHand = handLandmarks;
          } else if (handedness.label === 'Right') {
            handResults.rightHand = handLandmarks;
          }
        }
      }

      handResults.processingTime = performance.now() - startTime;
      this.emit('results', handResults);

    } catch (error) {
      Debug.error('MediaPipeHandsDetector', '处理结果失败', error);
      this.emit('error', error);
    }
  }

  /**
   * 检测手部
   */
  async detectHands(imageData: ImageData): Promise<HandResults> {
    if (!this.isInitialized || !this.hands) {
      throw new Error('MediaPipe手部检测器未初始化');
    }

    try {
      // 将ImageData转换为HTMLImageElement
      this.canvas.width = imageData.width;
      this.canvas.height = imageData.height;
      this.context.putImageData(imageData, 0, 0);

      // 发送到MediaPipe进行处理
      await this.hands.send({ image: this.canvas });

      // 返回一个Promise，等待结果回调
      return new Promise((resolve, reject) => {
        const timeout = setTimeout(() => {
          reject(new Error('检测超时'));
        }, 5000);

        const onResults = (results: HandResults) => {
          clearTimeout(timeout);
          this.off('results', onResults);
          this.off('error', onError);
          resolve(results);
        };

        const onError = (error: any) => {
          clearTimeout(timeout);
          this.off('results', onResults);
          this.off('error', onError);
          reject(error);
        };

        this.on('results', onResults);
        this.on('error', onError);
      });

    } catch (error) {
      Debug.error('MediaPipeHandsDetector', '手部检测失败', error);
      throw error;
    }
  }

  /**
   * 更新配置
   */
  async updateConfig(newConfig: Partial<MediaPipeHandsConfig>): Promise<void> {
    this.config = { ...this.config, ...newConfig };

    if (this.hands && this.isInitialized) {
      try {
        await this.hands.setOptions({
          maxNumHands: this.config.maxNumHands,
          modelComplexity: this.config.modelComplexity,
          minDetectionConfidence: this.config.minDetectionConfidence,
          minTrackingConfidence: this.config.minTrackingConfidence,
          staticImageMode: this.config.staticImageMode
        });

        Debug.log('MediaPipeHandsDetector', '配置更新成功');
      } catch (error) {
        Debug.error('MediaPipeHandsDetector', '配置更新失败', error);
        throw error;
      }
    }
  }

  /**
   * 销毁检测器
   */
  destroy(): void {
    if (this.hands) {
      this.hands.close();
      this.hands = null;
    }

    this.isInitialized = false;
    this.eventListeners.clear();

    Debug.log('MediaPipeHandsDetector', '检测器已销毁');
  }

  /**
   * 事件监听
   */
  on(event: string, callback: Function): void {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, []);
    }
    this.eventListeners.get(event)!.push(callback);
  }

  /**
   * 移除事件监听
   */
  off(event: string, callback: Function): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      const index = listeners.indexOf(callback);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    }
  }

  /**
   * 发出事件
   */
  private emit(event: string, data?: any): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      listeners.forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          Debug.error('MediaPipeHandsDetector', `事件回调执行失败: ${event}`, error);
        }
      });
    }
  }

  /**
   * 获取初始化状态
   */
  getInitialized(): boolean {
    return this.isInitialized;
  }

  /**
   * 检查MediaPipe是否可用
   */
  static isAvailable(): boolean {
    return typeof window !== 'undefined' && !!(window as any).Hands;
  }
}

/**
 * 手部追踪节点
 */
export class HandTrackingNode extends VisualScriptNode {
  /** 节点类型 */
  public static readonly TYPE = 'HandTracking';

  /** 节点名称 */
  public static readonly NAME = '手部追踪';

  /** 节点描述 */
  public static readonly DESCRIPTION = '使用MediaPipe检测手部关键点和识别手势，支持复杂手势算法';

  private handDetector: RealMediaPipeHandsDetector | null = null;
  private gestureRecognizer: AdvancedGestureRecognizer;
  private config: HandTrackingNodeConfig;
  private isInitialized = false;
  private lastResults: HandResults | null = null;
  private lastGestures: { left?: GestureResult; right?: GestureResult } = {};
  private processingCount = 0;
  private successCount = 0;
  private averageProcessingTime = 0;
  private isProcessing = false;
  private successCount = 0;

  /** 默认配置 */
  private static readonly DEFAULT_CONFIG: HandTrackingNodeConfig = {
    maxNumHands: 2,
    modelComplexity: 1,
    minDetectionConfidence: 0.5,
    minTrackingConfidence: 0.5,
    enableGestureRecognition: true,
    gestureConfidenceThreshold: 0.6,
    autoInitialize: true,
    staticImageMode: false,
    enableBimanualGestures: false
  };

  constructor(nodeType: string = HandTrackingNode.TYPE, name: string = HandTrackingNode.NAME, id?: string) {
    super(nodeType, name, id);

    this.config = { ...HandTrackingNode.DEFAULT_CONFIG };
    this.gestureRecognizer = new AdvancedGestureRecognizer();
    this.setupPorts();

    // 如果自动初始化，则立即初始化
    if (this.config.autoInitialize) {
      this.initializeDetector().catch(error => {
        Debug.error('HandTrackingNode', '自动初始化失败', error);
      });
    }
  }

  /**
   * 设置输入输出端口
   */
  private setupPorts(): void {
    // 输入端口
    this.addInput('imageData', 'object', '图像数据');
    this.addInput('detect', 'trigger', '检测');
    this.addInput('initialize', 'trigger', '初始化');
    this.addInput('maxNumHands', 'number', '最大手数');
    this.addInput('minDetectionConfidence', 'number', '检测置信度');
    this.addInput('enableGestureRecognition', 'boolean', '启用手势识别');

    // 基本输出端口
    this.addOutput('leftHand', 'array', '左手关键点');
    this.addOutput('rightHand', 'array', '右手关键点');
    this.addOutput('handedness', 'array', '手部分类');
    this.addOutput('confidence', 'number', '置信度');
    this.addOutput('handsDetected', 'boolean', '检测到手部');
    this.addOutput('handCount', 'number', '手部数量');
    this.addOutput('processingTime', 'number', '处理时间');
    this.addOutput('successRate', 'number', '成功率');

    // 事件输出端口
    this.addOutput('onDetected', 'trigger', '检测完成');
    this.addOutput('onInitialized', 'trigger', '初始化完成');
    this.addOutput('onError', 'trigger', '错误');

    // 手势识别输出端口
    this.addOutput('leftGesture', 'object', '左手手势');
    this.addOutput('rightGesture', 'object', '右手手势');
    this.addOutput('onGestureChanged', 'trigger', '手势变化');

    // 左手关键点输出
    this.addOutput('leftWrist', 'object', '左手腕');
    this.addOutput('leftThumb', 'array', '左拇指');
    this.addOutput('leftIndex', 'array', '左食指');
    this.addOutput('leftMiddle', 'array', '左中指');
    this.addOutput('leftRing', 'array', '左无名指');
    this.addOutput('leftPinky', 'array', '左小指');

    // 右手关键点输出
    this.addOutput('rightWrist', 'object', '右手腕');
    this.addOutput('rightThumb', 'array', '右拇指');
    this.addOutput('rightIndex', 'array', '右食指');
    this.addOutput('rightMiddle', 'array', '右中指');
    this.addOutput('rightRing', 'array', '右无名指');
    this.addOutput('rightPinky', 'array', '右小指');

    // 手部位置和方向输出
    this.addOutput('leftHandPosition', 'object', '左手位置');
    this.addOutput('rightHandPosition', 'object', '右手位置');
    this.addOutput('leftHandRotation', 'object', '左手旋转');
    this.addOutput('rightHandRotation', 'object', '右手旋转');
  }

  /**
   * 执行节点
   */
  public async execute(inputs?: any): Promise<any> {
    try {
      // 检查输入
      const imageData = inputs?.imageData as ImageData;
      const detectTrigger = inputs?.detect;
      const initializeTrigger = inputs?.initialize;
      const resetTrigger = inputs?.reset;
      const maxNumHands = inputs?.maxNumHands as number;
      const minDetectionConfidence = inputs?.minDetectionConfidence as number;
      const minTrackingConfidence = inputs?.minTrackingConfidence as number;
      const enableGestureRecognition = inputs?.enableGestureRecognition as boolean;

      // 更新配置
      let configChanged = false;
      if (maxNumHands !== undefined && maxNumHands !== this.config.maxNumHands) {
        this.config.maxNumHands = Math.max(1, Math.min(4, Math.floor(maxNumHands)));
        configChanged = true;
      }
      if (minDetectionConfidence !== undefined && minDetectionConfidence !== this.config.minDetectionConfidence) {
        this.config.minDetectionConfidence = Math.max(0, Math.min(1, minDetectionConfidence));
        configChanged = true;
      }
      if (minTrackingConfidence !== undefined && minTrackingConfidence !== this.config.minTrackingConfidence) {
        this.config.minTrackingConfidence = Math.max(0, Math.min(1, minTrackingConfidence));
        configChanged = true;
      }
      if (enableGestureRecognition !== undefined && enableGestureRecognition !== this.config.enableGestureRecognition) {
        this.config.enableGestureRecognition = enableGestureRecognition;
        configChanged = true;
      }

      // 如果配置改变且检测器已初始化，更新配置
      if (configChanged && this.isInitialized && this.handDetector) {
        await this.handDetector.updateConfig({
          maxNumHands: this.config.maxNumHands,
          modelComplexity: this.config.modelComplexity,
          minDetectionConfidence: this.config.minDetectionConfidence,
          minTrackingConfidence: this.config.minTrackingConfidence,
          staticImageMode: this.config.staticImageMode
        });
      }

      // 处理重置触发
      if (resetTrigger) {
        await this.resetDetector();
      }

      // 处理初始化触发
      if (initializeTrigger || (!this.isInitialized && this.config.autoInitialize)) {
        await this.initializeDetector();
      }

      // 处理检测触发
      if (detectTrigger && imageData && this.isInitialized && !this.isProcessing) {
        await this.detectHands(imageData);
      }

      // 返回输出
      return this.getNodeOutputs();

    } catch (error) {
      Debug.error('HandTrackingNode', '节点执行失败', String(error));
      return {
        onError: true,
        isInitialized: this.isInitialized,
        isProcessing: this.isProcessing
      };
    }
  }

  /**
   * 获取节点输出值
   */
  private getNodeOutputs(): any {
    const baseOutputs = {
      leftHand: [],
      rightHand: [],
      handedness: [],
      confidence: 0,
      handsDetected: false,
      handCount: 0,
      processingTime: 0,
      averageProcessingTime: this.averageProcessingTime,
      successRate: this.processingCount > 0 ? this.successCount / this.processingCount : 0,
      isProcessing: this.isProcessing,
      isInitialized: this.isInitialized,
      leftGesture: null,
      rightGesture: null,
      onDetected: false,
      onInitialized: false,
      onError: false,
      onGestureChanged: false
    };

    if (!this.lastResults) {
      return baseOutputs;
    }

    const results = this.lastResults;
    const handCount = (results.leftHand ? 1 : 0) + (results.rightHand ? 1 : 0);

    // 提取手指关键点
    const fingerOutputs: any = {};

    if (results.leftHand) {
      fingerOutputs.leftWrist = results.leftHand[0];
      fingerOutputs.leftThumb = results.leftHand.slice(1, 5);
      fingerOutputs.leftIndex = results.leftHand.slice(5, 9);
      fingerOutputs.leftMiddle = results.leftHand.slice(9, 13);
      fingerOutputs.leftRing = results.leftHand.slice(13, 17);
      fingerOutputs.leftPinky = results.leftHand.slice(17, 21);

      // 计算手部位置和方向
      fingerOutputs.leftHandPosition = new Vector3(
        results.leftHand[0].x,
        results.leftHand[0].y,
        results.leftHand[0].z || 0
      );
    }

    if (results.rightHand) {
      fingerOutputs.rightWrist = results.rightHand[0];
      fingerOutputs.rightThumb = results.rightHand.slice(1, 5);
      fingerOutputs.rightIndex = results.rightHand.slice(5, 9);
      fingerOutputs.rightMiddle = results.rightHand.slice(9, 13);
      fingerOutputs.rightRing = results.rightHand.slice(13, 17);
      fingerOutputs.rightPinky = results.rightHand.slice(17, 21);

      // 计算手部位置和方向
      fingerOutputs.rightHandPosition = new Vector3(
        results.rightHand[0].x,
        results.rightHand[0].y,
        results.rightHand[0].z || 0
      );
    }

    return {
      leftHand: results.leftHand || [],
      rightHand: results.rightHand || [],
      handedness: results.handedness || [],
      confidence: results.confidence,
      handsDetected: handCount > 0,
      handCount,
      processingTime: results.processingTime,
      averageProcessingTime: this.averageProcessingTime,
      successRate: this.processingCount > 0 ? this.successCount / this.processingCount : 0,
      isProcessing: this.isProcessing,
      isInitialized: this.isInitialized,
      leftGesture: this.lastGestures.left || null,
      rightGesture: this.lastGestures.right || null,
      onDetected: false,
      onInitialized: false,
      onError: false,
      onGestureChanged: false,
      ...fingerOutputs
    };
  }

  /**
   * 初始化检测器
   */
  private async initializeDetector(): Promise<void> {
    try {
      if (this.isInitialized) {
        Debug.log('HandTrackingNode', '检测器已初始化');
        return;
      }

      // 检查MediaPipe是否可用
      if (!RealMediaPipeHandsDetector.isAvailable()) {
        throw new Error('MediaPipe Hands库未加载，请确保已引入MediaPipe脚本');
      }

      // 创建检测器
      this.handDetector = new RealMediaPipeHandsDetector({
        maxNumHands: this.config.maxNumHands,
        modelComplexity: this.config.modelComplexity,
        minDetectionConfidence: this.config.minDetectionConfidence,
        minTrackingConfidence: this.config.minTrackingConfidence,
        staticImageMode: this.config.staticImageMode
      });

      // 设置事件监听
      this.setupDetectorEvents();

      // 初始化
      await this.handDetector.initialize();

      this.isInitialized = true;
      this.processingCount = 0;
      this.successCount = 0;
      this.averageProcessingTime = 0;

      Debug.log('HandTrackingNode', '手部检测器初始化成功');

    } catch (error) {
      Debug.error('HandTrackingNode', '初始化检测器失败', String(error));
      this.isInitialized = false;
      throw error;
    }
  }

  /**
   * 重置检测器
   */
  private async resetDetector(): Promise<void> {
    try {
      if (this.handDetector) {
        this.handDetector.destroy();
        this.handDetector = null;
      }

      this.isInitialized = false;
      this.lastResults = null;
      this.lastGestures = {};
      this.processingCount = 0;
      this.successCount = 0;
      this.averageProcessingTime = 0;
      this.isProcessing = false;
      this.gestureRecognizer.reset();

      Debug.log('HandTrackingNode', '检测器已重置');

    } catch (error) {
      Debug.error('HandTrackingNode', '重置检测器失败', String(error));
      throw error;
    }
  }

  /**
   * 设置检测器事件监听
   */
  private setupDetectorEvents(): void {
    if (!this.handDetector) return;

    this.handDetector.on('results', (results: HandResults) => {
      this.lastResults = results;
      this.successCount++;

      // 更新平均处理时间
      this.averageProcessingTime = (this.averageProcessingTime * (this.processingCount - 1) + results.processingTime) / this.processingCount;

      // 处理手势识别
      if (this.config.enableGestureRecognition) {
        this.processGestureRecognition(results);
      }

      this.isProcessing = false;
      Debug.log('HandTrackingNode', `手部检测完成，置信度: ${results.confidence.toFixed(3)}`);
    });

    this.handDetector.on('error', (error: any) => {
      this.isProcessing = false;
      Debug.error('HandTrackingNode', '检测器错误', error);
    });

    this.handDetector.on('initialized', () => {
      Debug.log('HandTrackingNode', '检测器初始化事件');
    });
  }

  /**
   * 检测手部
   */
  private async detectHands(imageData: ImageData): Promise<void> {
    if (!this.isInitialized || !this.handDetector) {
      Debug.error('HandTrackingNode', '检测器未初始化');
      return;
    }

    if (this.isProcessing) {
      Debug.warn('HandTrackingNode', '检测正在进行中，跳过此次检测');
      return;
    }

    try {
      this.isProcessing = true;
      this.processingCount++;

      // 执行检测
      const results = await this.handDetector.detectHands(imageData);

      // 结果已在事件回调中处理
      Debug.log('HandTrackingNode', '手部检测完成');

    } catch (error) {
      this.isProcessing = false;
      Debug.error('HandTrackingNode', '手部检测失败', String(error));
      throw error;
    }
  }

  /**
   * 处理手势识别
   */
  private processGestureRecognition(results: HandResults): void {
    const newGestures: { left?: GestureResult; right?: GestureResult } = {};

    // 识别左手手势
    if (results.leftHand && results.leftHand.length >= 21) {
      try {
        const leftGesture = this.gestureRecognizer.recognizeGesture(results.leftHand, 'left');
        if (leftGesture && leftGesture.confidence > this.config.gestureConfidenceThreshold) {
          newGestures.left = leftGesture;
        }
      } catch (error) {
        Debug.error('HandTrackingNode', '左手手势识别失败', error);
      }
    }

    // 识别右手手势
    if (results.rightHand && results.rightHand.length >= 21) {
      try {
        const rightGesture = this.gestureRecognizer.recognizeGesture(results.rightHand, 'right');
        if (rightGesture && rightGesture.confidence > this.config.gestureConfidenceThreshold) {
          newGestures.right = rightGesture;
        }
      } catch (error) {
        Debug.error('HandTrackingNode', '右手手势识别失败', error);
      }
    }

    // 检测双手协作手势
    if (this.config.enableBimanualGestures && results.leftHand && results.rightHand) {
      try {
        const bimanualGesture = this.detectBimanualGesture(results.leftHand, results.rightHand);
        if (bimanualGesture) {
          // 将双手手势信息添加到结果中
          if (newGestures.left) {
            newGestures.left.type = bimanualGesture.type;
            newGestures.left.confidence = Math.max(newGestures.left.confidence, bimanualGesture.confidence);
          }
          if (newGestures.right) {
            newGestures.right.type = bimanualGesture.type;
            newGestures.right.confidence = Math.max(newGestures.right.confidence, bimanualGesture.confidence);
          }
        }
      } catch (error) {
        Debug.error('HandTrackingNode', '双手协作手势识别失败', error);
      }
    }

    // 更新手势状态
    this.lastGestures = newGestures;
  }

  /**
   * 检测双手协作手势
   */
  private detectBimanualGesture(leftHand: LandmarkData[], rightHand: LandmarkData[]): { type: GestureType; confidence: number } | null {
    // 计算双手距离
    const leftWrist = leftHand[0];
    const rightWrist = rightHand[0];
    const distance = Math.sqrt(
      Math.pow(leftWrist.x - rightWrist.x, 2) +
      Math.pow(leftWrist.y - rightWrist.y, 2)
    );

    // 双手合拢手势
    if (distance < 0.1) {
      return { type: GestureType.GRAB, confidence: 0.8 };
    }

    // 双手张开手势
    if (distance > 0.3) {
      return { type: GestureType.OPEN_HAND, confidence: 0.7 };
    }

    return null;
  }

  /**
   * 获取节点配置
   */
  public getConfig(): HandTrackingNodeConfig {
    return { ...this.config };
  }

  /**
   * 更新节点配置
   */
  public async updateConfig(newConfig: Partial<HandTrackingNodeConfig>): Promise<void> {
    const oldConfig = { ...this.config };
    this.config = { ...this.config, ...newConfig };

    // 如果检测器已初始化，更新其配置
    if (this.handDetector && this.isInitialized) {
      try {
        await this.handDetector.updateConfig({
          maxNumHands: this.config.maxNumHands,
          modelComplexity: this.config.modelComplexity,
          minDetectionConfidence: this.config.minDetectionConfidence,
          minTrackingConfidence: this.config.minTrackingConfidence,
          staticImageMode: this.config.staticImageMode
        });
        Debug.log('HandTrackingNode', '配置更新成功', { oldConfig, newConfig: this.config });
      } catch (error) {
        Debug.error('HandTrackingNode', '配置更新失败', error);
        throw error;
      }
    }
  }

  /**
   * 获取最后检测结果
   */
  public getLastResults(): HandResults | null {
    return this.lastResults ? { ...this.lastResults } : null;
  }

  /**
   * 获取最后手势结果
   */
  public getLastGestures(): { left?: GestureResult; right?: GestureResult } {
    return { ...this.lastGestures };
  }

  /**
   * 获取检测成功率
   */
  public getSuccessRate(): number {
    return this.processingCount > 0 ? this.successCount / this.processingCount : 0;
  }

  /**
   * 获取平均处理时间
   */
  public getAverageProcessingTime(): number {
    return this.averageProcessingTime;
  }

  /**
   * 获取处理统计
   */
  public getProcessingStats(): {
    totalProcessed: number;
    successCount: number;
    successRate: number;
    averageProcessingTime: number;
  } {
    return {
      totalProcessed: this.processingCount,
      successCount: this.successCount,
      successRate: this.getSuccessRate(),
      averageProcessingTime: this.averageProcessingTime
    };
  }

  /**
   * 是否已初始化
   */
  public get initialized(): boolean {
    return this.isInitialized;
  }

  /**
   * 是否正在处理
   */
  public get processing(): boolean {
    return this.isProcessing;
  }

  /**
   * 检查MediaPipe是否可用
   */
  public static isMediaPipeAvailable(): boolean {
    return RealMediaPipeHandsDetector.isAvailable();
  }

  /**
   * 获取支持的手势类型
   */
  public static getSupportedGestures(): GestureType[] {
    return Object.values(GestureType);
  }

  /**
   * 计算两个手势的相似度
   */
  public static calculateGestureSimilarity(gesture1: GestureResult, gesture2: GestureResult): number {
    if (gesture1.type !== gesture2.type) {
      return 0;
    }

    const timeDiff = Math.abs(gesture1.timestamp - gesture2.timestamp);
    const positionDiff = gesture1.position.distanceTo(gesture2.position);
    const confidenceDiff = Math.abs(gesture1.confidence - gesture2.confidence);

    // 综合相似度计算
    const timeSimilarity = Math.max(0, 1 - timeDiff / 1000); // 1秒内为满分
    const positionSimilarity = Math.max(0, 1 - positionDiff);
    const confidenceSimilarity = Math.max(0, 1 - confidenceDiff);

    return (timeSimilarity + positionSimilarity + confidenceSimilarity) / 3;
  }

  /**
   * 销毁节点
   */
  public destroy(): void {
    if (this.handDetector) {
      this.handDetector.destroy();
      this.handDetector = null;
    }

    this.isInitialized = false;
    this.isProcessing = false;
    this.lastResults = null;
    this.lastGestures = {};
    this.processingCount = 0;
    this.successCount = 0;
    this.averageProcessingTime = 0;
    this.gestureRecognizer.reset();

    super.destroy?.();
  }

}

