/**
 * 音频节点集合
 * 提供音频播放、录制、处理、空间音频等功能的节点
 */
import { VisualScriptNode } from '../../visualscript/VisualScriptNode';
import { Debug } from '../../utils/Debug';
import { Vector3 } from 'three';
import { Entity } from '../entity/EntityNodes';

/**
 * 音频状态枚举
 */
export enum AudioState {
  STOPPED = 'stopped',
  PLAYING = 'playing',
  PAUSED = 'paused',
  LOADING = 'loading',
  ERROR = 'error'
}

/**
 * 音频源接口
 */
export interface AudioSource {
  id: string;
  url: string;
  buffer?: AudioBuffer;
  duration: number;
  loaded: boolean;
  loop: boolean;
  volume: number;
  pitch: number;
  startTime: number;
  endTime: number;
}

/**
 * 空间音频配置
 */
export interface SpatialAudioConfig {
  enabled: boolean;
  position: Vector3;
  maxDistance: number;
  rolloffFactor: number;
  coneInnerAngle: number;
  coneOuterAngle: number;
  coneOuterGain: number;
  orientation: Vector3;
}

/**
 * 音频管理器
 */
class AudioManager {
  private static instance: AudioManager;
  private audioContext: AudioContext | null = null;
  private masterGain: GainNode | null = null;
  private audioSources: Map<string, AudioSource> = new Map();
  private playingAudios: Map<string, AudioBufferSourceNode> = new Map();
  private spatialAudios: Map<string, PannerNode> = new Map();
  private listenerPosition: Vector3 = new Vector3(0, 0, 0);
  private listenerOrientation: Vector3 = new Vector3(0, 0, -1);

  private constructor() {
    this.initializeAudioContext();
  }

  public static getInstance(): AudioManager {
    if (!AudioManager.instance) {
      AudioManager.instance = new AudioManager();
    }
    return AudioManager.instance;
  }

  private async initializeAudioContext(): Promise<void> {
    try {
      this.audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
      this.masterGain = this.audioContext.createGain();
      this.masterGain.connect(this.audioContext.destination);

      // 设置空间音频监听器
      if (this.audioContext.listener) {
        this.updateListener();
      }

      Debug.log('AudioManager', '音频上下文初始化成功');
    } catch (error) {
      Debug.error('AudioManager', '音频上下文初始化失败', error);
    }
  }

  public async loadAudio(url: string): Promise<AudioSource> {
    try {
      if (!this.audioContext) {
        throw new Error('音频上下文未初始化');
      }

      const response = await fetch(url);
      const arrayBuffer = await response.arrayBuffer();
      const audioBuffer = await this.audioContext.decodeAudioData(arrayBuffer);

      const audioSource: AudioSource = {
        id: this.generateAudioId(),
        url,
        buffer: audioBuffer,
        duration: audioBuffer.duration,
        loaded: true,
        loop: false,
        volume: 1.0,
        pitch: 1.0,
        startTime: 0,
        endTime: audioBuffer.duration
      };

      this.audioSources.set(audioSource.id, audioSource);
      Debug.log('AudioManager', `音频加载成功: ${url}`);
      
      return audioSource;
    } catch (error) {
      Debug.error('AudioManager', `音频加载失败: ${url}`, error);
      throw error;
    }
  }

  public playAudio(audioId: string, config?: Partial<SpatialAudioConfig>): string {
    try {
      if (!this.audioContext || !this.masterGain) {
        throw new Error('音频上下文未初始化');
      }

      const audioSource = this.audioSources.get(audioId);
      if (!audioSource || !audioSource.buffer) {
        throw new Error('音频源不存在或未加载');
      }

      const source = this.audioContext.createBufferSource();
      source.buffer = audioSource.buffer;
      source.loop = audioSource.loop;
      source.playbackRate.value = audioSource.pitch;

      const gainNode = this.audioContext.createGain();
      gainNode.gain.value = audioSource.volume;

      let finalNode: AudioNode = gainNode;

      // 空间音频处理
      if (config?.enabled) {
        const pannerNode = this.audioContext.createPanner();
        this.configurePannerNode(pannerNode, config);
        
        gainNode.connect(pannerNode);
        pannerNode.connect(this.masterGain);
        finalNode = pannerNode;

        const playId = this.generatePlayId();
        this.spatialAudios.set(playId, pannerNode);
      } else {
        gainNode.connect(this.masterGain);
      }

      source.connect(gainNode);

      const playId = this.generatePlayId();
      this.playingAudios.set(playId, source);

      // 设置结束回调
      source.onended = () => {
        this.playingAudios.delete(playId);
        this.spatialAudios.delete(playId);
      };

      source.start(0, audioSource.startTime, audioSource.endTime - audioSource.startTime);
      
      Debug.log('AudioManager', `音频播放开始: ${audioId}`);
      return playId;
    } catch (error) {
      Debug.error('AudioManager', `音频播放失败: ${audioId}`, error);
      throw error;
    }
  }

  public stopAudio(playId: string): void {
    try {
      const source = this.playingAudios.get(playId);
      if (source) {
        source.stop();
        this.playingAudios.delete(playId);
        this.spatialAudios.delete(playId);
        Debug.log('AudioManager', `音频停止: ${playId}`);
      }
    } catch (error) {
      Debug.error('AudioManager', `音频停止失败: ${playId}`, error);
    }
  }

  public setMasterVolume(volume: number): void {
    if (this.masterGain) {
      this.masterGain.gain.value = Math.max(0, Math.min(1, volume));
    }
  }

  public setListenerPosition(position: Vector3): void {
    this.listenerPosition.copy(position);
    this.updateListener();
  }

  public setListenerOrientation(orientation: Vector3): void {
    this.listenerOrientation.copy(orientation);
    this.updateListener();
  }

  public updateSpatialAudio(playId: string, config: Partial<SpatialAudioConfig>): void {
    const pannerNode = this.spatialAudios.get(playId);
    if (pannerNode) {
      this.configurePannerNode(pannerNode, config);
    }
  }

  private configurePannerNode(pannerNode: PannerNode, config: Partial<SpatialAudioConfig>): void {
    if (config.position) {
      pannerNode.positionX.value = config.position.x;
      pannerNode.positionY.value = config.position.y;
      pannerNode.positionZ.value = config.position.z;
    }

    if (config.orientation) {
      pannerNode.orientationX.value = config.orientation.x;
      pannerNode.orientationY.value = config.orientation.y;
      pannerNode.orientationZ.value = config.orientation.z;
    }

    if (config.maxDistance !== undefined) {
      pannerNode.maxDistance = config.maxDistance;
    }

    if (config.rolloffFactor !== undefined) {
      pannerNode.rolloffFactor = config.rolloffFactor;
    }

    if (config.coneInnerAngle !== undefined) {
      pannerNode.coneInnerAngle = config.coneInnerAngle;
    }

    if (config.coneOuterAngle !== undefined) {
      pannerNode.coneOuterAngle = config.coneOuterAngle;
    }

    if (config.coneOuterGain !== undefined) {
      pannerNode.coneOuterGain = config.coneOuterGain;
    }
  }

  private updateListener(): void {
    if (!this.audioContext?.listener) return;

    const listener = this.audioContext.listener;
    
    if (listener.positionX) {
      listener.positionX.value = this.listenerPosition.x;
      listener.positionY.value = this.listenerPosition.y;
      listener.positionZ.value = this.listenerPosition.z;
    }

    if (listener.forwardX) {
      listener.forwardX.value = this.listenerOrientation.x;
      listener.forwardY.value = this.listenerOrientation.y;
      listener.forwardZ.value = this.listenerOrientation.z;
    }

    if (listener.upX) {
      listener.upX.value = 0;
      listener.upY.value = 1;
      listener.upZ.value = 0;
    }
  }

  private generateAudioId(): string {
    return 'audio_' + Math.random().toString(36).substr(2, 9);
  }

  private generatePlayId(): string {
    return 'play_' + Math.random().toString(36).substr(2, 9);
  }

  public getAudioContext(): AudioContext | null {
    return this.audioContext;
  }

  public isPlaying(playId: string): boolean {
    return this.playingAudios.has(playId);
  }

  public getPlayingCount(): number {
    return this.playingAudios.size;
  }
}

/**
 * 音频加载节点
 */
export class LoadAudioNode extends VisualScriptNode {
  public static readonly TYPE = 'LoadAudio';
  public static readonly NAME = '加载音频';
  public static readonly DESCRIPTION = '从URL加载音频文件';

  private audioManager: AudioManager;

  constructor(nodeType: string = LoadAudioNode.TYPE, name: string = LoadAudioNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.audioManager = AudioManager.getInstance();
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('load', 'trigger', '加载');
    this.addInput('url', 'string', '音频URL');
    this.addInput('preload', 'boolean', '预加载');

    // 输出端口
    this.addOutput('audioSource', 'object', '音频源');
    this.addOutput('audioId', 'string', '音频ID');
    this.addOutput('duration', 'number', '时长');
    this.addOutput('loaded', 'boolean', '已加载');
    this.addOutput('onLoaded', 'trigger', '加载完成');
    this.addOutput('onError', 'trigger', '加载失败');
  }

  public async execute(inputs?: any): Promise<any> {
    try {
      const loadTrigger = inputs?.load;
      const url = inputs?.url as string;
      const preload = inputs?.preload as boolean || false;

      if (!loadTrigger && !preload) {
        return this.getDefaultOutputs();
      }

      if (!url) {
        throw new Error('未提供音频URL');
      }

      const audioSource = await this.audioManager.loadAudio(url);

      return {
        audioSource,
        audioId: audioSource.id,
        duration: audioSource.duration,
        loaded: audioSource.loaded,
        onLoaded: true,
        onError: false
      };

    } catch (error) {
      Debug.error('LoadAudioNode', '加载音频失败', error);
      return {
        audioSource: null,
        audioId: '',
        duration: 0,
        loaded: false,
        onLoaded: false,
        onError: true
      };
    }
  }

  private getDefaultOutputs(): any {
    return {
      audioSource: null,
      audioId: '',
      duration: 0,
      loaded: false,
      onLoaded: false,
      onError: false
    };
  }
}

/**
 * 音频播放节点
 */
export class PlayAudioNode extends VisualScriptNode {
  public static readonly TYPE = 'PlayAudio';
  public static readonly NAME = '播放音频';
  public static readonly DESCRIPTION = '播放音频文件';

  private audioManager: AudioManager;

  constructor(nodeType: string = PlayAudioNode.TYPE, name: string = PlayAudioNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.audioManager = AudioManager.getInstance();
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('play', 'trigger', '播放');
    this.addInput('stop', 'trigger', '停止');
    this.addInput('audioId', 'string', '音频ID');
    this.addInput('volume', 'number', '音量');
    this.addInput('pitch', 'number', '音调');
    this.addInput('loop', 'boolean', '循环播放');
    this.addInput('startTime', 'number', '开始时间');
    this.addInput('endTime', 'number', '结束时间');

    // 输出端口
    this.addOutput('playId', 'string', '播放ID');
    this.addOutput('isPlaying', 'boolean', '正在播放');
    this.addOutput('currentTime', 'number', '当前时间');
    this.addOutput('onPlay', 'trigger', '播放时');
    this.addOutput('onStop', 'trigger', '停止时');
    this.addOutput('onEnd', 'trigger', '结束时');
    this.addOutput('onError', 'trigger', '错误时');
  }

  public execute(inputs?: any): any {
    try {
      const playTrigger = inputs?.play;
      const stopTrigger = inputs?.stop;
      const audioId = inputs?.audioId as string;

      if (playTrigger && audioId) {
        return this.playAudio(inputs);
      } else if (stopTrigger) {
        return this.stopAudio(inputs);
      }

      return this.getDefaultOutputs();

    } catch (error) {
      Debug.error('PlayAudioNode', '音频播放操作失败', error);
      return {
        ...this.getDefaultOutputs(),
        onError: true
      };
    }
  }

  private playAudio(inputs: any): any {
    const audioId = inputs.audioId as string;
    const volume = inputs.volume as number || 1.0;
    const pitch = inputs.pitch as number || 1.0;
    const loop = inputs.loop as boolean || false;
    const startTime = inputs.startTime as number || 0;
    const endTime = inputs.endTime as number;

    try {
      // 这里需要先设置音频源属性，然后播放
      // 简化实现，直接播放
      const playId = this.audioManager.playAudio(audioId);

      return {
        playId,
        isPlaying: true,
        currentTime: startTime,
        onPlay: true,
        onStop: false,
        onEnd: false,
        onError: false
      };

    } catch (error) {
      Debug.error('PlayAudioNode', '播放音频失败', error);
      return {
        ...this.getDefaultOutputs(),
        onError: true
      };
    }
  }

  private stopAudio(inputs: any): any {
    const playId = inputs.playId as string;

    if (playId) {
      this.audioManager.stopAudio(playId);
    }

    return {
      playId: playId || '',
      isPlaying: false,
      currentTime: 0,
      onPlay: false,
      onStop: true,
      onEnd: false,
      onError: false
    };
  }

  private getDefaultOutputs(): any {
    return {
      playId: '',
      isPlaying: false,
      currentTime: 0,
      onPlay: false,
      onStop: false,
      onEnd: false,
      onError: false
    };
  }
}

/**
 * 空间音频节点
 */
export class SpatialAudioNode extends VisualScriptNode {
  public static readonly TYPE = 'SpatialAudio';
  public static readonly NAME = '空间音频';
  public static readonly DESCRIPTION = '创建3D空间音频效果';

  private audioManager: AudioManager;

  constructor(nodeType: string = SpatialAudioNode.TYPE, name: string = SpatialAudioNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.audioManager = AudioManager.getInstance();
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('play', 'trigger', '播放');
    this.addInput('update', 'trigger', '更新');
    this.addInput('audioId', 'string', '音频ID');
    this.addInput('entity', 'object', '音源实体');
    this.addInput('position', 'object', '音源位置');
    this.addInput('maxDistance', 'number', '最大距离');
    this.addInput('rolloffFactor', 'number', '衰减因子');
    this.addInput('volume', 'number', '音量');

    // 输出端口
    this.addOutput('playId', 'string', '播放ID');
    this.addOutput('isPlaying', 'boolean', '正在播放');
    this.addOutput('distance', 'number', '距离');
    this.addOutput('effectiveVolume', 'number', '有效音量');
    this.addOutput('onPlay', 'trigger', '播放时');
    this.addOutput('onUpdate', 'trigger', '更新时');
  }

  public execute(inputs?: any): any {
    try {
      const playTrigger = inputs?.play;
      const updateTrigger = inputs?.update;
      const audioId = inputs?.audioId as string;
      const entity = inputs?.entity as Entity;
      const position = inputs?.position as Vector3;

      if (playTrigger && audioId) {
        return this.playSpatialAudio(inputs);
      } else if (updateTrigger) {
        return this.updateSpatialAudio(inputs);
      }

      return this.getDefaultOutputs();

    } catch (error) {
      Debug.error('SpatialAudioNode', '空间音频操作失败', error);
      return this.getDefaultOutputs();
    }
  }

  private playSpatialAudio(inputs: any): any {
    const audioId = inputs.audioId as string;
    const entity = inputs.entity as Entity;
    const position = inputs.position as Vector3 || (entity ? entity.transform.position : new Vector3());
    const maxDistance = inputs.maxDistance as number || 100;
    const rolloffFactor = inputs.rolloffFactor as number || 1;
    const volume = inputs.volume as number || 1;

    const spatialConfig: SpatialAudioConfig = {
      enabled: true,
      position,
      maxDistance,
      rolloffFactor,
      coneInnerAngle: 360,
      coneOuterAngle: 360,
      coneOuterGain: 0,
      orientation: new Vector3(0, 0, -1)
    };

    try {
      const playId = this.audioManager.playAudio(audioId, spatialConfig);
      
      return {
        playId,
        isPlaying: true,
        distance: 0,
        effectiveVolume: volume,
        onPlay: true,
        onUpdate: false
      };

    } catch (error) {
      Debug.error('SpatialAudioNode', '播放空间音频失败', error);
      return this.getDefaultOutputs();
    }
  }

  private updateSpatialAudio(inputs: any): any {
    const playId = inputs.playId as string;
    const entity = inputs.entity as Entity;
    const position = inputs.position as Vector3 || (entity ? entity.transform.position : new Vector3());

    if (playId) {
      this.audioManager.updateSpatialAudio(playId, { position });
    }

    return {
      playId: playId || '',
      isPlaying: this.audioManager.isPlaying(playId),
      distance: 0,
      effectiveVolume: 1,
      onPlay: false,
      onUpdate: true
    };
  }

  private getDefaultOutputs(): any {
    return {
      playId: '',
      isPlaying: false,
      distance: 0,
      effectiveVolume: 0,
      onPlay: false,
      onUpdate: false
    };
  }
}

/**
 * 音频监听器节点
 */
export class AudioListenerNode extends VisualScriptNode {
  public static readonly TYPE = 'AudioListener';
  public static readonly NAME = '音频监听器';
  public static readonly DESCRIPTION = '设置3D音频监听器位置和方向';

  private audioManager: AudioManager;

  constructor(nodeType: string = AudioListenerNode.TYPE, name: string = AudioListenerNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.audioManager = AudioManager.getInstance();
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('update', 'trigger', '更新');
    this.addInput('entity', 'object', '监听器实体');
    this.addInput('position', 'object', '监听器位置');
    this.addInput('orientation', 'object', '监听器方向');
    this.addInput('masterVolume', 'number', '主音量');

    // 输出端口
    this.addOutput('listenerPosition', 'object', '监听器位置');
    this.addOutput('listenerOrientation', 'object', '监听器方向');
    this.addOutput('currentVolume', 'number', '当前音量');
    this.addOutput('onUpdate', 'trigger', '更新完成');
  }

  public execute(inputs?: any): any {
    try {
      const updateTrigger = inputs?.update;
      const entity = inputs?.entity as Entity;
      const position = inputs?.position as Vector3 || (entity ? entity.transform.position : new Vector3());
      const orientation = inputs?.orientation as Vector3 || new Vector3(0, 0, -1);
      const masterVolume = inputs?.masterVolume as number;

      if (updateTrigger || entity) {
        this.audioManager.setListenerPosition(position);
        this.audioManager.setListenerOrientation(orientation);

        if (masterVolume !== undefined) {
          this.audioManager.setMasterVolume(masterVolume);
        }
      }

      return {
        listenerPosition: position,
        listenerOrientation: orientation,
        currentVolume: masterVolume || 1,
        onUpdate: updateTrigger || !!entity
      };

    } catch (error) {
      Debug.error('AudioListenerNode', '音频监听器更新失败', error);
      return this.getDefaultOutputs();
    }
  }

  private getDefaultOutputs(): any {
    return {
      listenerPosition: new Vector3(0, 0, 0),
      listenerOrientation: new Vector3(0, 0, -1),
      currentVolume: 1,
      onUpdate: false
    };
  }
}
