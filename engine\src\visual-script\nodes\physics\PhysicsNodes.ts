/**
 * 物理节点集合
 * 提供刚体物理、碰撞检测、力学模拟等功能的节点
 */
import { VisualScriptNode } from '../../visualscript/VisualScriptNode';
import { Debug } from '../../utils/Debug';
import { Vector3, Quaternion, Box3, Sphere } from 'three';
import { Entity } from '../entity/EntityNodes';

/**
 * 刚体组件接口
 */
export interface RigidBodyComponent {
  type: 'static' | 'dynamic' | 'kinematic';
  mass: number;
  velocity: Vector3;
  angularVelocity: Vector3;
  friction: number;
  restitution: number;
  linearDamping: number;
  angularDamping: number;
  isKinematic: boolean;
  isTrigger: boolean;
  freezeRotation: boolean;
  useGravity: boolean;
  centerOfMass: Vector3;
  inertiaTensor: Vector3;
}

/**
 * 碰撞体组件接口
 */
export interface ColliderComponent {
  shape: 'box' | 'sphere' | 'capsule' | 'mesh' | 'plane';
  size: Vector3;
  radius: number;
  height: number;
  isTrigger: boolean;
  material: {
    friction: number;
    restitution: number;
    density: number;
  };
  offset: Vector3;
}

/**
 * 碰撞信息接口
 */
export interface CollisionInfo {
  entityA: Entity;
  entityB: Entity;
  contactPoint: Vector3;
  contactNormal: Vector3;
  penetrationDepth: number;
  relativeVelocity: Vector3;
  impulse: number;
  timestamp: number;
}

/**
 * 添加刚体节点
 */
export class AddRigidBodyNode extends VisualScriptNode {
  public static readonly TYPE = 'AddRigidBody';
  public static readonly NAME = '添加刚体';
  public static readonly DESCRIPTION = '为实体添加刚体物理组件';

  constructor(nodeType: string = AddRigidBodyNode.TYPE, name: string = AddRigidBodyNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('add', 'trigger', '添加');
    this.addInput('entity', 'object', '目标实体');
    this.addInput('bodyType', 'string', '刚体类型');
    this.addInput('mass', 'number', '质量');
    this.addInput('friction', 'number', '摩擦力');
    this.addInput('restitution', 'number', '弹性系数');
    this.addInput('linearDamping', 'number', '线性阻尼');
    this.addInput('angularDamping', 'number', '角阻尼');
    this.addInput('useGravity', 'boolean', '使用重力');
    this.addInput('isKinematic', 'boolean', '运动学模式');
    this.addInput('freezeRotation', 'boolean', '冻结旋转');

    // 输出端口
    this.addOutput('rigidBody', 'object', '刚体组件');
    this.addOutput('entity', 'object', '实体');
    this.addOutput('success', 'boolean', '添加成功');
    this.addOutput('onAdded', 'trigger', '添加完成');
    this.addOutput('onError', 'trigger', '添加失败');
  }

  public execute(inputs?: any): any {
    try {
      const addTrigger = inputs?.add;
      if (!addTrigger) {
        return this.getDefaultOutputs();
      }

      const entity = inputs?.entity as Entity;
      if (!entity) {
        throw new Error('未提供目标实体');
      }

      const bodyType = inputs?.bodyType as string || 'dynamic';
      const mass = inputs?.mass as number || 1.0;
      const friction = inputs?.friction as number || 0.5;
      const restitution = inputs?.restitution as number || 0.3;
      const linearDamping = inputs?.linearDamping as number || 0.1;
      const angularDamping = inputs?.angularDamping as number || 0.1;
      const useGravity = inputs?.useGravity as boolean ?? true;
      const isKinematic = inputs?.isKinematic as boolean || false;
      const freezeRotation = inputs?.freezeRotation as boolean || false;

      // 创建刚体组件
      const rigidBody: RigidBodyComponent = {
        type: bodyType as any,
        mass,
        velocity: new Vector3(0, 0, 0),
        angularVelocity: new Vector3(0, 0, 0),
        friction,
        restitution,
        linearDamping,
        angularDamping,
        isKinematic,
        isTrigger: false,
        freezeRotation,
        useGravity,
        centerOfMass: new Vector3(0, 0, 0),
        inertiaTensor: new Vector3(1, 1, 1)
      };

      // 添加到实体
      entity.components.set('RigidBody', rigidBody);

      Debug.log('AddRigidBodyNode', `刚体组件添加成功: ${entity.name} (${bodyType})`);

      return {
        rigidBody,
        entity,
        success: true,
        onAdded: true,
        onError: false
      };

    } catch (error) {
      Debug.error('AddRigidBodyNode', '添加刚体失败', error);
      return {
        rigidBody: null,
        entity: null,
        success: false,
        onAdded: false,
        onError: true
      };
    }
  }

  private getDefaultOutputs(): any {
    return {
      rigidBody: null,
      entity: null,
      success: false,
      onAdded: false,
      onError: false
    };
  }
}

/**
 * 添加碰撞体节点
 */
export class AddColliderNode extends VisualScriptNode {
  public static readonly TYPE = 'AddCollider';
  public static readonly NAME = '添加碰撞体';
  public static readonly DESCRIPTION = '为实体添加碰撞体组件';

  constructor(nodeType: string = AddColliderNode.TYPE, name: string = AddColliderNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('add', 'trigger', '添加');
    this.addInput('entity', 'object', '目标实体');
    this.addInput('shape', 'string', '碰撞体形状');
    this.addInput('size', 'object', '尺寸');
    this.addInput('radius', 'number', '半径');
    this.addInput('height', 'number', '高度');
    this.addInput('isTrigger', 'boolean', '触发器模式');
    this.addInput('friction', 'number', '摩擦力');
    this.addInput('restitution', 'number', '弹性系数');
    this.addInput('density', 'number', '密度');
    this.addInput('offset', 'object', '偏移位置');

    // 输出端口
    this.addOutput('collider', 'object', '碰撞体组件');
    this.addOutput('entity', 'object', '实体');
    this.addOutput('success', 'boolean', '添加成功');
    this.addOutput('onAdded', 'trigger', '添加完成');
    this.addOutput('onError', 'trigger', '添加失败');
  }

  public execute(inputs?: any): any {
    try {
      const addTrigger = inputs?.add;
      if (!addTrigger) {
        return this.getDefaultOutputs();
      }

      const entity = inputs?.entity as Entity;
      if (!entity) {
        throw new Error('未提供目标实体');
      }

      const shape = inputs?.shape as string || 'box';
      const size = inputs?.size as Vector3 || new Vector3(1, 1, 1);
      const radius = inputs?.radius as number || 0.5;
      const height = inputs?.height as number || 1.0;
      const isTrigger = inputs?.isTrigger as boolean || false;
      const friction = inputs?.friction as number || 0.5;
      const restitution = inputs?.restitution as number || 0.3;
      const density = inputs?.density as number || 1.0;
      const offset = inputs?.offset as Vector3 || new Vector3(0, 0, 0);

      // 创建碰撞体组件
      const collider: ColliderComponent = {
        shape: shape as any,
        size,
        radius,
        height,
        isTrigger,
        material: {
          friction,
          restitution,
          density
        },
        offset
      };

      // 添加到实体
      entity.components.set('Collider', collider);

      Debug.log('AddColliderNode', `碰撞体组件添加成功: ${entity.name} (${shape})`);

      return {
        collider,
        entity,
        success: true,
        onAdded: true,
        onError: false
      };

    } catch (error) {
      Debug.error('AddColliderNode', '添加碰撞体失败', error);
      return {
        collider: null,
        entity: null,
        success: false,
        onAdded: false,
        onError: true
      };
    }
  }

  private getDefaultOutputs(): any {
    return {
      collider: null,
      entity: null,
      success: false,
      onAdded: false,
      onError: false
    };
  }
}

/**
 * 施加力节点
 */
export class ApplyForceNode extends VisualScriptNode {
  public static readonly TYPE = 'ApplyForce';
  public static readonly NAME = '施加力';
  public static readonly DESCRIPTION = '对刚体施加力';

  constructor(nodeType: string = ApplyForceNode.TYPE, name: string = ApplyForceNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('apply', 'trigger', '施加');
    this.addInput('entity', 'object', '目标实体');
    this.addInput('force', 'object', '力向量');
    this.addInput('position', 'object', '施力位置');
    this.addInput('forceMode', 'string', '力模式');
    this.addInput('duration', 'number', '持续时间');

    // 输出端口
    this.addOutput('entity', 'object', '实体');
    this.addOutput('appliedForce', 'object', '施加的力');
    this.addOutput('resultingVelocity', 'object', '结果速度');
    this.addOutput('onApplied', 'trigger', '施加完成');
    this.addOutput('onError', 'trigger', '施加失败');
  }

  public execute(inputs?: any): any {
    try {
      const applyTrigger = inputs?.apply;
      if (!applyTrigger) {
        return this.getDefaultOutputs();
      }

      const entity = inputs?.entity as Entity;
      if (!entity) {
        throw new Error('未提供目标实体');
      }

      const rigidBody = entity.components.get('RigidBody') as RigidBodyComponent;
      if (!rigidBody) {
        throw new Error('实体没有刚体组件');
      }

      const force = inputs?.force as Vector3 || new Vector3(0, 0, 0);
      const position = inputs?.position as Vector3;
      const forceMode = inputs?.forceMode as string || 'force';
      const duration = inputs?.duration as number || 0;

      // 根据力模式计算速度变化
      let deltaVelocity = new Vector3();
      
      switch (forceMode) {
        case 'force':
          // F = ma, a = F/m, v = at (假设dt=1/60)
          deltaVelocity = force.clone().divideScalar(rigidBody.mass).multiplyScalar(1/60);
          break;
        case 'impulse':
          // J = mv, v = J/m
          deltaVelocity = force.clone().divideScalar(rigidBody.mass);
          break;
        case 'acceleration':
          // a = F, v = at
          deltaVelocity = force.clone().multiplyScalar(1/60);
          break;
        case 'velocityChange':
          // 直接改变速度
          deltaVelocity = force.clone();
          break;
      }

      // 应用力
      rigidBody.velocity.add(deltaVelocity);

      // 如果指定了位置，计算扭矩
      if (position) {
        const torque = new Vector3().crossVectors(position, force);
        const angularAcceleration = torque.clone().divide(rigidBody.inertiaTensor);
        rigidBody.angularVelocity.add(angularAcceleration.multiplyScalar(1/60));
      }

      Debug.log('ApplyForceNode', `力已施加: ${entity.name} 力(${force.x}, ${force.y}, ${force.z})`);

      return {
        entity,
        appliedForce: force,
        resultingVelocity: rigidBody.velocity.clone(),
        onApplied: true,
        onError: false
      };

    } catch (error) {
      Debug.error('ApplyForceNode', '施加力失败', error);
      return {
        entity: null,
        appliedForce: new Vector3(0, 0, 0),
        resultingVelocity: new Vector3(0, 0, 0),
        onApplied: false,
        onError: true
      };
    }
  }

  private getDefaultOutputs(): any {
    return {
      entity: null,
      appliedForce: new Vector3(0, 0, 0),
      resultingVelocity: new Vector3(0, 0, 0),
      onApplied: false,
      onError: false
    };
  }
}

/**
 * 设置速度节点
 */
export class SetVelocityNode extends VisualScriptNode {
  public static readonly TYPE = 'SetVelocity';
  public static readonly NAME = '设置速度';
  public static readonly DESCRIPTION = '设置刚体的线性和角速度';

  constructor(nodeType: string = SetVelocityNode.TYPE, name: string = SetVelocityNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('set', 'trigger', '设置');
    this.addInput('entity', 'object', '目标实体');
    this.addInput('velocity', 'object', '线性速度');
    this.addInput('angularVelocity', 'object', '角速度');
    this.addInput('additive', 'boolean', '累加模式');

    // 输出端口
    this.addOutput('entity', 'object', '实体');
    this.addOutput('newVelocity', 'object', '新线性速度');
    this.addOutput('newAngularVelocity', 'object', '新角速度');
    this.addOutput('speed', 'number', '速度大小');
    this.addOutput('onSet', 'trigger', '设置完成');
  }

  public execute(inputs?: any): any {
    try {
      const setTrigger = inputs?.set;
      if (!setTrigger) {
        return this.getDefaultOutputs();
      }

      const entity = inputs?.entity as Entity;
      if (!entity) {
        throw new Error('未提供目标实体');
      }

      const rigidBody = entity.components.get('RigidBody') as RigidBodyComponent;
      if (!rigidBody) {
        throw new Error('实体没有刚体组件');
      }

      const velocity = inputs?.velocity as Vector3;
      const angularVelocity = inputs?.angularVelocity as Vector3;
      const additive = inputs?.additive as boolean || false;

      // 设置线性速度
      if (velocity) {
        if (additive) {
          rigidBody.velocity.add(velocity);
        } else {
          rigidBody.velocity.copy(velocity);
        }
      }

      // 设置角速度
      if (angularVelocity) {
        if (additive) {
          rigidBody.angularVelocity.add(angularVelocity);
        } else {
          rigidBody.angularVelocity.copy(angularVelocity);
        }
      }

      const speed = rigidBody.velocity.length();

      Debug.log('SetVelocityNode', `速度已设置: ${entity.name} 速度大小=${speed.toFixed(2)}`);

      return {
        entity,
        newVelocity: rigidBody.velocity.clone(),
        newAngularVelocity: rigidBody.angularVelocity.clone(),
        speed,
        onSet: true
      };

    } catch (error) {
      Debug.error('SetVelocityNode', '设置速度失败', error);
      return this.getDefaultOutputs();
    }
  }

  private getDefaultOutputs(): any {
    return {
      entity: null,
      newVelocity: new Vector3(0, 0, 0),
      newAngularVelocity: new Vector3(0, 0, 0),
      speed: 0,
      onSet: false
    };
  }
}

/**
 * 碰撞检测节点
 */
export class CollisionDetectionNode extends VisualScriptNode {
  public static readonly TYPE = 'CollisionDetection';
  public static readonly NAME = '碰撞检测';
  public static readonly DESCRIPTION = '检测实体之间的碰撞';

  private collisionHistory: CollisionInfo[] = [];

  constructor(nodeType: string = CollisionDetectionNode.TYPE, name: string = CollisionDetectionNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('entityA', 'object', '实体A');
    this.addInput('entityB', 'object', '实体B');
    this.addInput('check', 'trigger', '检测');
    this.addInput('continuous', 'boolean', '连续检测');

    // 输出端口
    this.addOutput('isColliding', 'boolean', '是否碰撞');
    this.addOutput('collisionInfo', 'object', '碰撞信息');
    this.addOutput('contactPoint', 'object', '接触点');
    this.addOutput('contactNormal', 'object', '接触法线');
    this.addOutput('penetrationDepth', 'number', '穿透深度');
    this.addOutput('relativeVelocity', 'object', '相对速度');
    this.addOutput('onCollisionEnter', 'trigger', '碰撞开始');
    this.addOutput('onCollisionStay', 'trigger', '碰撞持续');
    this.addOutput('onCollisionExit', 'trigger', '碰撞结束');
  }

  public execute(inputs?: any): any {
    try {
      const entityA = inputs?.entityA as Entity;
      const entityB = inputs?.entityB as Entity;
      const checkTrigger = inputs?.check;
      const continuous = inputs?.continuous as boolean || false;

      if (!entityA || !entityB) {
        return this.getDefaultOutputs();
      }

      const colliderA = entityA.components.get('Collider') as ColliderComponent;
      const colliderB = entityB.components.get('Collider') as ColliderComponent;

      if (!colliderA || !colliderB) {
        return this.getDefaultOutputs();
      }

      // 执行碰撞检测
      const collisionResult = this.checkCollision(entityA, entityB, colliderA, colliderB);

      // 检查碰撞状态变化
      const collisionStates = this.updateCollisionStates(entityA, entityB, collisionResult);

      return {
        isColliding: collisionResult.isColliding,
        collisionInfo: collisionResult.info,
        contactPoint: collisionResult.info?.contactPoint || new Vector3(),
        contactNormal: collisionResult.info?.contactNormal || new Vector3(),
        penetrationDepth: collisionResult.info?.penetrationDepth || 0,
        relativeVelocity: collisionResult.info?.relativeVelocity || new Vector3(),
        onCollisionEnter: collisionStates.enter,
        onCollisionStay: collisionStates.stay,
        onCollisionExit: collisionStates.exit
      };

    } catch (error) {
      Debug.error('CollisionDetectionNode', '碰撞检测失败', error);
      return this.getDefaultOutputs();
    }
  }

  private checkCollision(entityA: Entity, entityB: Entity, colliderA: ColliderComponent, colliderB: ColliderComponent): {
    isColliding: boolean;
    info?: CollisionInfo;
  } {
    // 简化的碰撞检测实现
    const posA = entityA.transform.position;
    const posB = entityB.transform.position;
    const distance = posA.distanceTo(posB);

    // 基于形状的粗略碰撞检测
    let collisionDistance = 0;

    if (colliderA.shape === 'sphere' && colliderB.shape === 'sphere') {
      collisionDistance = colliderA.radius + colliderB.radius;
    } else if (colliderA.shape === 'box' && colliderB.shape === 'box') {
      const sizeA = colliderA.size;
      const sizeB = colliderB.size;
      collisionDistance = Math.max(sizeA.x, sizeA.y, sizeA.z) + Math.max(sizeB.x, sizeB.y, sizeB.z);
    } else {
      // 混合形状的简化处理
      collisionDistance = 1.0;
    }

    const isColliding = distance < collisionDistance;

    if (isColliding) {
      // 计算碰撞信息
      const contactPoint = posA.clone().lerp(posB, 0.5);
      const contactNormal = posB.clone().sub(posA).normalize();
      const penetrationDepth = collisionDistance - distance;

      // 计算相对速度
      const rigidBodyA = entityA.components.get('RigidBody') as RigidBodyComponent;
      const rigidBodyB = entityB.components.get('RigidBody') as RigidBodyComponent;
      const relativeVelocity = new Vector3();

      if (rigidBodyA && rigidBodyB) {
        relativeVelocity.subVectors(rigidBodyB.velocity, rigidBodyA.velocity);
      }

      const collisionInfo: CollisionInfo = {
        entityA,
        entityB,
        contactPoint,
        contactNormal,
        penetrationDepth,
        relativeVelocity,
        impulse: relativeVelocity.length(),
        timestamp: Date.now()
      };

      return { isColliding: true, info: collisionInfo };
    }

    return { isColliding: false };
  }

  private updateCollisionStates(entityA: Entity, entityB: Entity, collisionResult: any): {
    enter: boolean;
    stay: boolean;
    exit: boolean;
  } {
    const pairKey = `${entityA.id}_${entityB.id}`;
    const wasColliding = this.collisionHistory.some(info =>
      (info.entityA.id === entityA.id && info.entityB.id === entityB.id) ||
      (info.entityA.id === entityB.id && info.entityB.id === entityA.id)
    );

    const isColliding = collisionResult.isColliding;

    // 更新历史记录
    if (isColliding && collisionResult.info) {
      this.collisionHistory.push(collisionResult.info);
      // 限制历史记录长度
      if (this.collisionHistory.length > 100) {
        this.collisionHistory.shift();
      }
    }

    return {
      enter: isColliding && !wasColliding,
      stay: isColliding && wasColliding,
      exit: !isColliding && wasColliding
    };
  }

  private getDefaultOutputs(): any {
    return {
      isColliding: false,
      collisionInfo: null,
      contactPoint: new Vector3(),
      contactNormal: new Vector3(),
      penetrationDepth: 0,
      relativeVelocity: new Vector3(),
      onCollisionEnter: false,
      onCollisionStay: false,
      onCollisionExit: false
    };
  }
}

/**
 * 射线投射节点
 */
export class RaycastNode extends VisualScriptNode {
  public static readonly TYPE = 'Raycast';
  public static readonly NAME = '射线投射';
  public static readonly DESCRIPTION = '从指定位置发射射线进行碰撞检测';

  constructor(nodeType: string = RaycastNode.TYPE, name: string = RaycastNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('cast', 'trigger', '投射');
    this.addInput('origin', 'object', '起始位置');
    this.addInput('direction', 'object', '方向');
    this.addInput('distance', 'number', '最大距离');
    this.addInput('layerMask', 'number', '层级掩码');
    this.addInput('entities', 'array', '检测实体列表');

    // 输出端口
    this.addOutput('hit', 'boolean', '是否命中');
    this.addOutput('hitEntity', 'object', '命中实体');
    this.addOutput('hitPoint', 'object', '命中点');
    this.addOutput('hitNormal', 'object', '命中法线');
    this.addOutput('hitDistance', 'number', '命中距离');
    this.addOutput('allHits', 'array', '所有命中');
    this.addOutput('onHit', 'trigger', '命中时');
    this.addOutput('onMiss', 'trigger', '未命中');
  }

  public execute(inputs?: any): any {
    try {
      const castTrigger = inputs?.cast;
      if (!castTrigger) {
        return this.getDefaultOutputs();
      }

      const origin = inputs?.origin as Vector3 || new Vector3(0, 0, 0);
      const direction = inputs?.direction as Vector3 || new Vector3(0, 0, 1);
      const distance = inputs?.distance as number || 100;
      const layerMask = inputs?.layerMask as number || -1;
      const entities = inputs?.entities as Entity[] || [];

      // 执行射线投射
      const raycastResult = this.performRaycast(origin, direction, distance, layerMask, entities);

      return {
        hit: raycastResult.hit,
        hitEntity: raycastResult.hitEntity,
        hitPoint: raycastResult.hitPoint,
        hitNormal: raycastResult.hitNormal,
        hitDistance: raycastResult.hitDistance,
        allHits: raycastResult.allHits,
        onHit: raycastResult.hit,
        onMiss: !raycastResult.hit
      };

    } catch (error) {
      Debug.error('RaycastNode', '射线投射失败', error);
      return this.getDefaultOutputs();
    }
  }

  private performRaycast(origin: Vector3, direction: Vector3, distance: number, layerMask: number, entities: Entity[]): any {
    const normalizedDirection = direction.clone().normalize();
    const hits: any[] = [];

    for (const entity of entities) {
      // 检查层级掩码
      if (layerMask !== -1 && (entity.layer & layerMask) === 0) {
        continue;
      }

      const collider = entity.components.get('Collider') as ColliderComponent;
      if (!collider) {
        continue;
      }

      // 简化的射线-碰撞体相交测试
      const hitResult = this.rayIntersectCollider(origin, normalizedDirection, distance, entity, collider);
      if (hitResult.hit) {
        hits.push(hitResult);
      }
    }

    // 按距离排序
    hits.sort((a, b) => a.distance - b.distance);

    const closestHit = hits.length > 0 ? hits[0] : null;

    return {
      hit: !!closestHit,
      hitEntity: closestHit?.entity || null,
      hitPoint: closestHit?.point || new Vector3(),
      hitNormal: closestHit?.normal || new Vector3(),
      hitDistance: closestHit?.distance || 0,
      allHits: hits
    };
  }

  private rayIntersectCollider(origin: Vector3, direction: Vector3, maxDistance: number, entity: Entity, collider: ColliderComponent): any {
    const entityPos = entity.transform.position;

    if (collider.shape === 'sphere') {
      return this.rayIntersectSphere(origin, direction, maxDistance, entityPos, collider.radius, entity);
    } else if (collider.shape === 'box') {
      return this.rayIntersectBox(origin, direction, maxDistance, entityPos, collider.size, entity);
    }

    return { hit: false };
  }

  private rayIntersectSphere(origin: Vector3, direction: Vector3, maxDistance: number, center: Vector3, radius: number, entity: Entity): any {
    const oc = origin.clone().sub(center);
    const a = direction.dot(direction);
    const b = 2.0 * oc.dot(direction);
    const c = oc.dot(oc) - radius * radius;
    const discriminant = b * b - 4 * a * c;

    if (discriminant < 0) {
      return { hit: false };
    }

    const t = (-b - Math.sqrt(discriminant)) / (2.0 * a);

    if (t > 0 && t <= maxDistance) {
      const hitPoint = origin.clone().add(direction.clone().multiplyScalar(t));
      const hitNormal = hitPoint.clone().sub(center).normalize();

      return {
        hit: true,
        entity,
        point: hitPoint,
        normal: hitNormal,
        distance: t
      };
    }

    return { hit: false };
  }

  private rayIntersectBox(origin: Vector3, direction: Vector3, maxDistance: number, center: Vector3, size: Vector3, entity: Entity): any {
    // 简化的AABB射线相交测试
    const min = center.clone().sub(size.clone().multiplyScalar(0.5));
    const max = center.clone().add(size.clone().multiplyScalar(0.5));

    const invDir = new Vector3(1 / direction.x, 1 / direction.y, 1 / direction.z);

    const t1 = (min.x - origin.x) * invDir.x;
    const t2 = (max.x - origin.x) * invDir.x;
    const t3 = (min.y - origin.y) * invDir.y;
    const t4 = (max.y - origin.y) * invDir.y;
    const t5 = (min.z - origin.z) * invDir.z;
    const t6 = (max.z - origin.z) * invDir.z;

    const tmin = Math.max(Math.max(Math.min(t1, t2), Math.min(t3, t4)), Math.min(t5, t6));
    const tmax = Math.min(Math.min(Math.max(t1, t2), Math.max(t3, t4)), Math.max(t5, t6));

    if (tmax < 0 || tmin > tmax || tmin > maxDistance) {
      return { hit: false };
    }

    const t = tmin > 0 ? tmin : tmax;
    const hitPoint = origin.clone().add(direction.clone().multiplyScalar(t));

    // 计算法线（简化）
    const hitNormal = hitPoint.clone().sub(center).normalize();

    return {
      hit: true,
      entity,
      point: hitPoint,
      normal: hitNormal,
      distance: t
    };
  }

  private getDefaultOutputs(): any {
    return {
      hit: false,
      hitEntity: null,
      hitPoint: new Vector3(),
      hitNormal: new Vector3(),
      hitDistance: 0,
      allHits: [],
      onHit: false,
      onMiss: false
    };
  }
}
