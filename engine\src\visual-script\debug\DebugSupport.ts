/**
 * 视觉脚本调试支持系统
 * 提供断点、变量监视、执行追踪等调试功能
 */
import { VisualScriptNode } from '../visualscript/VisualScriptNode';
import { EditorIntegration } from '../editor/EditorIntegration';
import { Debug } from '../utils/Debug';

/**
 * 断点类型枚举
 */
export enum BreakpointType {
  NODE = 'node',
  CONNECTION = 'connection',
  CONDITION = 'condition',
  EXCEPTION = 'exception'
}

/**
 * 断点状态枚举
 */
export enum BreakpointState {
  ENABLED = 'enabled',
  DISABLED = 'disabled',
  HIT = 'hit'
}

/**
 * 执行状态枚举
 */
export enum ExecutionState {
  STOPPED = 'stopped',
  RUNNING = 'running',
  PAUSED = 'paused',
  STEPPING = 'stepping',
  ERROR = 'error'
}

/**
 * 断点信息接口
 */
export interface BreakpointInfo {
  id: string;
  type: BreakpointType;
  nodeId?: string;
  connectionId?: string;
  condition?: string;
  state: BreakpointState;
  hitCount: number;
  createdAt: number;
}

/**
 * 变量监视信息接口
 */
export interface WatchVariable {
  id: string;
  name: string;
  expression: string;
  value: any;
  type: string;
  nodeId?: string;
  lastUpdated: number;
}

/**
 * 执行追踪信息接口
 */
export interface ExecutionTrace {
  id: string;
  nodeId: string;
  nodeType: string;
  timestamp: number;
  inputs: any;
  outputs: any;
  executionTime: number;
  error?: string;
}

/**
 * 调用栈信息接口
 */
export interface CallStackFrame {
  nodeId: string;
  nodeType: string;
  nodeName: string;
  depth: number;
  timestamp: number;
}

/**
 * 调试会话接口
 */
export interface DebugSession {
  id: string;
  name: string;
  state: ExecutionState;
  startTime: number;
  endTime?: number;
  breakpoints: Map<string, BreakpointInfo>;
  watchVariables: Map<string, WatchVariable>;
  executionTraces: ExecutionTrace[];
  callStack: CallStackFrame[];
  currentNodeId?: string;
}

/**
 * 调试支持管理器
 */
class DebugSupportManager {
  private sessions: Map<string, DebugSession> = new Map();
  private currentSession: DebugSession | null = null;
  private eventListeners: Map<string, Function[]> = new Map();
  private stepMode: 'into' | 'over' | 'out' | null = null;
  private maxTraceEntries: number = 1000;

  /**
   * 创建调试会话
   */
  createSession(name: string = 'Debug Session'): string {
    const sessionId = this.generateSessionId();
    const session: DebugSession = {
      id: sessionId,
      name,
      state: ExecutionState.STOPPED,
      startTime: Date.now(),
      breakpoints: new Map(),
      watchVariables: new Map(),
      executionTraces: [],
      callStack: []
    };

    this.sessions.set(sessionId, session);
    this.currentSession = session;

    this.emit('sessionCreated', { sessionId, name });
    Debug.log('DebugSupport', `调试会话创建: ${sessionId} (${name})`);

    return sessionId;
  }

  /**
   * 开始调试
   */
  startDebugging(sessionId?: string): boolean {
    const session = sessionId ? this.sessions.get(sessionId) : this.currentSession;
    if (!session) {
      Debug.warn('DebugSupport', '调试会话不存在');
      return false;
    }

    session.state = ExecutionState.RUNNING;
    session.startTime = Date.now();
    session.executionTraces = [];
    session.callStack = [];

    this.currentSession = session;
    this.emit('debugStarted', { sessionId: session.id });
    
    Debug.log('DebugSupport', `调试开始: ${session.id}`);
    return true;
  }

  /**
   * 停止调试
   */
  stopDebugging(): void {
    if (!this.currentSession) {
      return;
    }

    this.currentSession.state = ExecutionState.STOPPED;
    this.currentSession.endTime = Date.now();
    this.currentSession.callStack = [];
    this.currentSession.currentNodeId = undefined;

    this.emit('debugStopped', { sessionId: this.currentSession.id });
    Debug.log('DebugSupport', `调试停止: ${this.currentSession.id}`);
  }

  /**
   * 暂停调试
   */
  pauseDebugging(): void {
    if (!this.currentSession || this.currentSession.state !== ExecutionState.RUNNING) {
      return;
    }

    this.currentSession.state = ExecutionState.PAUSED;
    this.emit('debugPaused', { sessionId: this.currentSession.id });
    Debug.log('DebugSupport', '调试暂停');
  }

  /**
   * 继续调试
   */
  continueDebugging(): void {
    if (!this.currentSession || this.currentSession.state !== ExecutionState.PAUSED) {
      return;
    }

    this.currentSession.state = ExecutionState.RUNNING;
    this.stepMode = null;
    this.emit('debugContinued', { sessionId: this.currentSession.id });
    Debug.log('DebugSupport', '调试继续');
  }

  /**
   * 单步执行
   */
  stepInto(): void {
    if (!this.currentSession) {
      return;
    }

    this.currentSession.state = ExecutionState.STEPPING;
    this.stepMode = 'into';
    this.emit('stepInto', { sessionId: this.currentSession.id });
    Debug.log('DebugSupport', '单步进入');
  }

  /**
   * 单步跳过
   */
  stepOver(): void {
    if (!this.currentSession) {
      return;
    }

    this.currentSession.state = ExecutionState.STEPPING;
    this.stepMode = 'over';
    this.emit('stepOver', { sessionId: this.currentSession.id });
    Debug.log('DebugSupport', '单步跳过');
  }

  /**
   * 单步跳出
   */
  stepOut(): void {
    if (!this.currentSession) {
      return;
    }

    this.currentSession.state = ExecutionState.STEPPING;
    this.stepMode = 'out';
    this.emit('stepOut', { sessionId: this.currentSession.id });
    Debug.log('DebugSupport', '单步跳出');
  }

  /**
   * 添加断点
   */
  addBreakpoint(nodeId: string, condition?: string): string {
    if (!this.currentSession) {
      throw new Error('没有活动的调试会话');
    }

    const breakpointId = this.generateBreakpointId();
    const breakpoint: BreakpointInfo = {
      id: breakpointId,
      type: BreakpointType.NODE,
      nodeId,
      condition,
      state: BreakpointState.ENABLED,
      hitCount: 0,
      createdAt: Date.now()
    };

    this.currentSession.breakpoints.set(breakpointId, breakpoint);
    this.emit('breakpointAdded', { breakpointId, nodeId, condition });
    
    Debug.log('DebugSupport', `断点添加: ${nodeId} (${breakpointId})`);
    return breakpointId;
  }

  /**
   * 移除断点
   */
  removeBreakpoint(breakpointId: string): boolean {
    if (!this.currentSession) {
      return false;
    }

    const removed = this.currentSession.breakpoints.delete(breakpointId);
    if (removed) {
      this.emit('breakpointRemoved', { breakpointId });
      Debug.log('DebugSupport', `断点移除: ${breakpointId}`);
    }

    return removed;
  }

  /**
   * 切换断点状态
   */
  toggleBreakpoint(breakpointId: string): boolean {
    if (!this.currentSession) {
      return false;
    }

    const breakpoint = this.currentSession.breakpoints.get(breakpointId);
    if (!breakpoint) {
      return false;
    }

    breakpoint.state = breakpoint.state === BreakpointState.ENABLED 
      ? BreakpointState.DISABLED 
      : BreakpointState.ENABLED;

    this.emit('breakpointToggled', { breakpointId, state: breakpoint.state });
    Debug.log('DebugSupport', `断点状态切换: ${breakpointId} -> ${breakpoint.state}`);

    return true;
  }

  /**
   * 添加变量监视
   */
  addWatchVariable(name: string, expression: string, nodeId?: string): string {
    if (!this.currentSession) {
      throw new Error('没有活动的调试会话');
    }

    const watchId = this.generateWatchId();
    const watchVariable: WatchVariable = {
      id: watchId,
      name,
      expression,
      value: undefined,
      type: 'unknown',
      nodeId,
      lastUpdated: Date.now()
    };

    this.currentSession.watchVariables.set(watchId, watchVariable);
    this.emit('watchVariableAdded', { watchId, name, expression, nodeId });
    
    Debug.log('DebugSupport', `变量监视添加: ${name} (${watchId})`);
    return watchId;
  }

  /**
   * 移除变量监视
   */
  removeWatchVariable(watchId: string): boolean {
    if (!this.currentSession) {
      return false;
    }

    const removed = this.currentSession.watchVariables.delete(watchId);
    if (removed) {
      this.emit('watchVariableRemoved', { watchId });
      Debug.log('DebugSupport', `变量监视移除: ${watchId}`);
    }

    return removed;
  }

  /**
   * 更新变量监视值
   */
  updateWatchVariable(watchId: string, value: any, type: string): void {
    if (!this.currentSession) {
      return;
    }

    const watchVariable = this.currentSession.watchVariables.get(watchId);
    if (watchVariable) {
      watchVariable.value = value;
      watchVariable.type = type;
      watchVariable.lastUpdated = Date.now();

      this.emit('watchVariableUpdated', { watchId, value, type });
    }
  }

  /**
   * 节点执行前钩子
   */
  onNodeExecuteBefore(node: VisualScriptNode, inputs: any): boolean {
    if (!this.currentSession || this.currentSession.state === ExecutionState.STOPPED) {
      return true; // 继续执行
    }

    // 更新当前节点
    this.currentSession.currentNodeId = node.id;

    // 添加到调用栈
    this.pushCallStack(node);

    // 检查断点
    if (this.shouldBreakAtNode(node.id)) {
      this.hitBreakpoint(node.id);
      return false; // 暂停执行
    }

    // 检查单步模式
    if (this.stepMode) {
      this.currentSession.state = ExecutionState.PAUSED;
      this.stepMode = null;
      this.emit('stepCompleted', { nodeId: node.id });
      return false; // 暂停执行
    }

    return true; // 继续执行
  }

  /**
   * 节点执行后钩子
   */
  onNodeExecuteAfter(node: VisualScriptNode, inputs: any, outputs: any, executionTime: number, error?: string): void {
    if (!this.currentSession) {
      return;
    }

    // 记录执行追踪
    this.addExecutionTrace(node, inputs, outputs, executionTime, error);

    // 更新变量监视
    this.updateWatchVariables(node, outputs);

    // 从调用栈移除
    this.popCallStack();

    // 如果有错误，暂停调试
    if (error) {
      this.currentSession.state = ExecutionState.ERROR;
      this.emit('executionError', { nodeId: node.id, error });
    }
  }

  /**
   * 检查是否应该在节点处断点
   */
  private shouldBreakAtNode(nodeId: string): boolean {
    if (!this.currentSession) {
      return false;
    }

    for (const breakpoint of this.currentSession.breakpoints.values()) {
      if (breakpoint.nodeId === nodeId && 
          breakpoint.state === BreakpointState.ENABLED &&
          this.evaluateBreakpointCondition(breakpoint)) {
        return true;
      }
    }

    return false;
  }

  /**
   * 评估断点条件
   */
  private evaluateBreakpointCondition(breakpoint: BreakpointInfo): boolean {
    if (!breakpoint.condition) {
      return true;
    }

    try {
      // 简化的条件评估
      // 实际实现应该有更复杂的表达式解析
      return eval(breakpoint.condition);
    } catch (error) {
      Debug.warn('DebugSupport', `断点条件评估失败: ${breakpoint.condition}`, error);
      return true;
    }
  }

  /**
   * 命中断点
   */
  private hitBreakpoint(nodeId: string): void {
    if (!this.currentSession) {
      return;
    }

    // 更新断点命中次数
    for (const breakpoint of this.currentSession.breakpoints.values()) {
      if (breakpoint.nodeId === nodeId && breakpoint.state === BreakpointState.ENABLED) {
        breakpoint.hitCount++;
        breakpoint.state = BreakpointState.HIT;
      }
    }

    this.currentSession.state = ExecutionState.PAUSED;
    this.emit('breakpointHit', { nodeId });
    Debug.log('DebugSupport', `断点命中: ${nodeId}`);
  }

  /**
   * 添加执行追踪
   */
  private addExecutionTrace(node: VisualScriptNode, inputs: any, outputs: any, executionTime: number, error?: string): void {
    if (!this.currentSession) {
      return;
    }

    const trace: ExecutionTrace = {
      id: this.generateTraceId(),
      nodeId: node.id,
      nodeType: node.nodeType,
      timestamp: Date.now(),
      inputs,
      outputs,
      executionTime,
      error
    };

    this.currentSession.executionTraces.push(trace);

    // 限制追踪条目数量
    if (this.currentSession.executionTraces.length > this.maxTraceEntries) {
      this.currentSession.executionTraces.shift();
    }

    this.emit('executionTraceAdded', { trace });
  }

  /**
   * 更新变量监视
   */
  private updateWatchVariables(node: VisualScriptNode, outputs: any): void {
    if (!this.currentSession) {
      return;
    }

    for (const watchVariable of this.currentSession.watchVariables.values()) {
      if (!watchVariable.nodeId || watchVariable.nodeId === node.id) {
        try {
          // 简化的表达式评估
          const value = this.evaluateWatchExpression(watchVariable.expression, outputs);
          const type = typeof value;
          this.updateWatchVariable(watchVariable.id, value, type);
        } catch (error) {
          this.updateWatchVariable(watchVariable.id, `Error: ${error.message}`, 'error');
        }
      }
    }
  }

  /**
   * 评估监视表达式
   */
  private evaluateWatchExpression(expression: string, context: any): any {
    // 简化的表达式评估
    if (context && context.hasOwnProperty(expression)) {
      return context[expression];
    }
    
    try {
      return eval(expression);
    } catch (error) {
      throw new Error(`表达式评估失败: ${expression}`);
    }
  }

  /**
   * 推入调用栈
   */
  private pushCallStack(node: VisualScriptNode): void {
    if (!this.currentSession) {
      return;
    }

    const frame: CallStackFrame = {
      nodeId: node.id,
      nodeType: node.nodeType,
      nodeName: node.name,
      depth: this.currentSession.callStack.length,
      timestamp: Date.now()
    };

    this.currentSession.callStack.push(frame);
  }

  /**
   * 弹出调用栈
   */
  private popCallStack(): void {
    if (!this.currentSession) {
      return;
    }

    this.currentSession.callStack.pop();
  }

  /**
   * 获取当前会话
   */
  getCurrentSession(): DebugSession | null {
    return this.currentSession;
  }

  /**
   * 获取所有会话
   */
  getAllSessions(): DebugSession[] {
    return Array.from(this.sessions.values());
  }

  /**
   * 清理会话
   */
  clearSession(sessionId: string): boolean {
    const removed = this.sessions.delete(sessionId);
    if (removed && this.currentSession?.id === sessionId) {
      this.currentSession = null;
    }
    return removed;
  }

  // ID生成器
  private generateSessionId(): string {
    return 'session_' + Math.random().toString(36).substr(2, 9);
  }

  private generateBreakpointId(): string {
    return 'bp_' + Math.random().toString(36).substr(2, 9);
  }

  private generateWatchId(): string {
    return 'watch_' + Math.random().toString(36).substr(2, 9);
  }

  private generateTraceId(): string {
    return 'trace_' + Math.random().toString(36).substr(2, 9);
  }

  // 事件系统
  on(event: string, callback: Function): void {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, []);
    }
    this.eventListeners.get(event)!.push(callback);
  }

  off(event: string, callback: Function): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      const index = listeners.indexOf(callback);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    }
  }

  private emit(event: string, data?: any): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      listeners.forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          Debug.error('DebugSupport', `事件回调执行失败: ${event}`, error);
        }
      });
    }
  }
}

// 导出单例实例
export const DebugSupport = new DebugSupportManager();
