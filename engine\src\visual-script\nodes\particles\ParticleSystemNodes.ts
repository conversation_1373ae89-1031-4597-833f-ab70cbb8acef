/**
 * 粒子系统节点集合
 * 提供粒子发射、动画、物理模拟等功能的节点
 */
import { VisualScriptNode } from '../../visualscript/VisualScriptNode';
import { Debug } from '../../utils/Debug';
import { Vector3, Vector2, Color, Quaternion } from 'three';
import { Entity } from '../entity/EntityNodes';

/**
 * 粒子类型枚举
 */
export enum ParticleType {
  POINT = 'point',
  SPRITE = 'sprite',
  MESH = 'mesh',
  TRAIL = 'trail',
  RIBBON = 'ribbon'
}

/**
 * 发射器形状枚举
 */
export enum EmitterShape {
  POINT = 'point',
  SPHERE = 'sphere',
  BOX = 'box',
  CONE = 'cone',
  CYLINDER = 'cylinder',
  CIRCLE = 'circle',
  LINE = 'line'
}

/**
 * 粒子混合模式枚举
 */
export enum BlendMode {
  NORMAL = 'normal',
  ADDITIVE = 'additive',
  MULTIPLY = 'multiply',
  SCREEN = 'screen',
  OVERLAY = 'overlay'
}

/**
 * 粒子接口
 */
export interface Particle {
  id: string;
  position: Vector3;
  velocity: Vector3;
  acceleration: Vector3;
  rotation: Quaternion;
  angularVelocity: Vector3;
  scale: Vector3;
  color: Color;
  alpha: number;
  size: number;
  mass: number;
  lifetime: number;
  age: number;
  active: boolean;
  userData: any;
}

/**
 * 粒子发射器配置
 */
export interface EmitterConfig {
  shape: EmitterShape;
  rate: number;
  burst: number;
  duration: number;
  loop: boolean;
  prewarm: boolean;
  position: Vector3;
  rotation: Vector3;
  scale: Vector3;
  emissionArea: Vector3;
  emissionDirection: Vector3;
  emissionAngle: number;
  emissionSpeed: number;
  emissionSpeedVariation: number;
}

/**
 * 粒子属性配置
 */
export interface ParticleProperties {
  type: ParticleType;
  lifetime: number;
  lifetimeVariation: number;
  startSize: number;
  endSize: number;
  sizeVariation: number;
  startColor: Color;
  endColor: Color;
  colorVariation: number;
  startAlpha: number;
  endAlpha: number;
  alphaVariation: number;
  startRotation: number;
  endRotation: number;
  rotationVariation: number;
  mass: number;
  massVariation: number;
}

/**
 * 粒子物理配置
 */
export interface ParticlePhysics {
  gravity: Vector3;
  drag: number;
  angularDrag: number;
  bounce: number;
  friction: number;
  collisionEnabled: boolean;
  collisionLayers: number[];
  windInfluence: number;
  turbulence: number;
  turbulenceFrequency: number;
}

/**
 * 粒子渲染配置
 */
export interface ParticleRenderConfig {
  blendMode: BlendMode;
  depthWrite: boolean;
  depthTest: boolean;
  sortParticles: boolean;
  faceCameraMode: 'none' | 'y' | 'free';
  textureAtlas: boolean;
  atlasRows: number;
  atlasCols: number;
  animationSpeed: number;
}

/**
 * 高级粒子系统
 */
class AdvancedParticleSystem {
  private particles: Map<string, Particle> = new Map();
  private emitters: Map<string, EmitterConfig> = new Map();
  private particleProperties: Map<string, ParticleProperties> = new Map();
  private particlePhysics: Map<string, ParticlePhysics> = new Map();
  private renderConfigs: Map<string, ParticleRenderConfig> = new Map();
  private activeEmitters: Set<string> = new Set();
  private simulationTime: number = 0;
  private timeStep: number = 1/60;
  private maxParticles: number = 10000;
  private particlePool: Particle[] = [];

  /**
   * 创建发射器
   */
  createEmitter(id: string, config: EmitterConfig, properties: ParticleProperties, physics: ParticlePhysics, renderConfig: ParticleRenderConfig): void {
    this.emitters.set(id, config);
    this.particleProperties.set(id, properties);
    this.particlePhysics.set(id, physics);
    this.renderConfigs.set(id, renderConfig);
    
    Debug.log('AdvancedParticleSystem', `粒子发射器创建: ${id}`);
  }

  /**
   * 启动发射器
   */
  startEmitter(id: string): void {
    if (this.emitters.has(id)) {
      this.activeEmitters.add(id);
      Debug.log('AdvancedParticleSystem', `粒子发射器启动: ${id}`);
    }
  }

  /**
   * 停止发射器
   */
  stopEmitter(id: string): void {
    this.activeEmitters.delete(id);
    Debug.log('AdvancedParticleSystem', `粒子发射器停止: ${id}`);
  }

  /**
   * 更新粒子系统
   */
  updateSystem(): void {
    this.simulationTime += this.timeStep;
    
    // 更新发射器
    for (const emitterId of this.activeEmitters) {
      this.updateEmitter(emitterId);
    }
    
    // 更新粒子
    for (const particle of this.particles.values()) {
      this.updateParticle(particle);
    }
    
    // 清理死亡粒子
    this.cleanupDeadParticles();
  }

  /**
   * 更新发射器
   */
  private updateEmitter(emitterId: string): void {
    const emitter = this.emitters.get(emitterId);
    const properties = this.particleProperties.get(emitterId);
    
    if (!emitter || !properties) return;
    
    // 计算本帧应发射的粒子数量
    const particlesToEmit = Math.floor(emitter.rate * this.timeStep);
    
    for (let i = 0; i < particlesToEmit; i++) {
      if (this.particles.size >= this.maxParticles) break;
      this.emitParticle(emitterId, emitter, properties);
    }
  }

  /**
   * 发射粒子
   */
  private emitParticle(emitterId: string, emitter: EmitterConfig, properties: ParticleProperties): void {
    const particle = this.getParticleFromPool();
    
    // 设置初始位置
    particle.position.copy(this.calculateEmissionPosition(emitter));
    
    // 设置初始速度
    particle.velocity.copy(this.calculateEmissionVelocity(emitter));
    
    // 设置初始属性
    this.initializeParticleProperties(particle, properties);
    
    // 添加到活跃粒子列表
    this.particles.set(particle.id, particle);
  }

  /**
   * 计算发射位置
   */
  private calculateEmissionPosition(emitter: EmitterConfig): Vector3 {
    const position = emitter.position.clone();
    
    switch (emitter.shape) {
      case EmitterShape.POINT:
        break;
      case EmitterShape.SPHERE:
        const sphereRadius = emitter.emissionArea.x;
        const sphereDir = new Vector3(
          Math.random() - 0.5,
          Math.random() - 0.5,
          Math.random() - 0.5
        ).normalize();
        position.add(sphereDir.multiplyScalar(Math.random() * sphereRadius));
        break;
      case EmitterShape.BOX:
        position.add(new Vector3(
          (Math.random() - 0.5) * emitter.emissionArea.x,
          (Math.random() - 0.5) * emitter.emissionArea.y,
          (Math.random() - 0.5) * emitter.emissionArea.z
        ));
        break;
      case EmitterShape.CIRCLE:
        const circleRadius = emitter.emissionArea.x;
        const angle = Math.random() * Math.PI * 2;
        position.add(new Vector3(
          Math.cos(angle) * Math.random() * circleRadius,
          0,
          Math.sin(angle) * Math.random() * circleRadius
        ));
        break;
      case EmitterShape.LINE:
        const lineLength = emitter.emissionArea.x;
        position.add(new Vector3(
          (Math.random() - 0.5) * lineLength,
          0,
          0
        ));
        break;
    }
    
    return position;
  }

  /**
   * 计算发射速度
   */
  private calculateEmissionVelocity(emitter: EmitterConfig): Vector3 {
    const baseSpeed = emitter.emissionSpeed;
    const speedVariation = emitter.emissionSpeedVariation;
    const speed = baseSpeed + (Math.random() - 0.5) * speedVariation;
    
    let direction = emitter.emissionDirection.clone().normalize();
    
    // 添加角度变化
    if (emitter.emissionAngle > 0) {
      const angle = (Math.random() - 0.5) * emitter.emissionAngle * Math.PI / 180;
      const rotationAxis = new Vector3(
        Math.random() - 0.5,
        Math.random() - 0.5,
        Math.random() - 0.5
      ).normalize();
      
      direction.applyAxisAngle(rotationAxis, angle);
    }
    
    return direction.multiplyScalar(speed);
  }

  /**
   * 初始化粒子属性
   */
  private initializeParticleProperties(particle: Particle, properties: ParticleProperties): void {
    // 生命周期
    particle.lifetime = properties.lifetime + (Math.random() - 0.5) * properties.lifetimeVariation;
    particle.age = 0;
    particle.active = true;
    
    // 尺寸
    particle.size = properties.startSize + (Math.random() - 0.5) * properties.sizeVariation;
    particle.scale.setScalar(particle.size);
    
    // 颜色
    particle.color.copy(properties.startColor);
    particle.alpha = properties.startAlpha + (Math.random() - 0.5) * properties.alphaVariation;
    
    // 旋转
    const rotation = properties.startRotation + (Math.random() - 0.5) * properties.rotationVariation;
    particle.rotation.setFromAxisAngle(new Vector3(0, 0, 1), rotation);
    
    // 质量
    particle.mass = properties.mass + (Math.random() - 0.5) * properties.massVariation;
    
    // 重置加速度
    particle.acceleration.set(0, 0, 0);
    particle.angularVelocity.set(0, 0, 0);
  }

  /**
   * 更新粒子
   */
  private updateParticle(particle: Particle): void {
    if (!particle.active) return;
    
    // 更新年龄
    particle.age += this.timeStep;
    
    // 检查生命周期
    if (particle.age >= particle.lifetime) {
      particle.active = false;
      return;
    }
    
    // 计算生命周期进度
    const lifeProgress = particle.age / particle.lifetime;
    
    // 更新物理
    this.updateParticlePhysics(particle);
    
    // 更新外观
    this.updateParticleAppearance(particle, lifeProgress);
  }

  /**
   * 更新粒子物理
   */
  private updateParticlePhysics(particle: Particle): void {
    // 应用重力（这里需要从物理配置获取）
    const gravity = new Vector3(0, -9.81, 0);
    particle.acceleration.add(gravity);
    
    // 应用阻力
    const drag = 0.1;
    const dragForce = particle.velocity.clone().multiplyScalar(-drag);
    particle.acceleration.add(dragForce.divideScalar(particle.mass));
    
    // 更新速度
    particle.velocity.add(particle.acceleration.clone().multiplyScalar(this.timeStep));
    
    // 更新位置
    particle.position.add(particle.velocity.clone().multiplyScalar(this.timeStep));
    
    // 重置加速度
    particle.acceleration.set(0, 0, 0);
  }

  /**
   * 更新粒子外观
   */
  private updateParticleAppearance(particle: Particle, lifeProgress: number): void {
    // 这里可以根据生命周期进度更新颜色、大小、透明度等
    // 简化实现，只更新透明度
    particle.alpha = 1 - lifeProgress;
  }

  /**
   * 清理死亡粒子
   */
  private cleanupDeadParticles(): void {
    const deadParticles: string[] = [];
    
    for (const [id, particle] of this.particles) {
      if (!particle.active) {
        deadParticles.push(id);
        this.returnParticleToPool(particle);
      }
    }
    
    for (const id of deadParticles) {
      this.particles.delete(id);
    }
  }

  /**
   * 从粒子池获取粒子
   */
  private getParticleFromPool(): Particle {
    if (this.particlePool.length > 0) {
      return this.particlePool.pop()!;
    }
    
    return this.createNewParticle();
  }

  /**
   * 创建新粒子
   */
  private createNewParticle(): Particle {
    return {
      id: this.generateParticleId(),
      position: new Vector3(),
      velocity: new Vector3(),
      acceleration: new Vector3(),
      rotation: new Quaternion(),
      angularVelocity: new Vector3(),
      scale: new Vector3(1, 1, 1),
      color: new Color(1, 1, 1),
      alpha: 1,
      size: 1,
      mass: 1,
      lifetime: 1,
      age: 0,
      active: false,
      userData: {}
    };
  }

  /**
   * 将粒子返回池中
   */
  private returnParticleToPool(particle: Particle): void {
    // 重置粒子状态
    particle.active = false;
    particle.age = 0;
    particle.position.set(0, 0, 0);
    particle.velocity.set(0, 0, 0);
    particle.acceleration.set(0, 0, 0);
    
    this.particlePool.push(particle);
  }

  /**
   * 生成粒子ID
   */
  private generateParticleId(): string {
    return 'particle_' + Math.random().toString(36).substr(2, 9);
  }

  /**
   * 获取活跃粒子
   */
  getActiveParticles(): Particle[] {
    return Array.from(this.particles.values()).filter(p => p.active);
  }

  /**
   * 获取粒子数量
   */
  getParticleCount(): number {
    return this.particles.size;
  }

  /**
   * 清理系统
   */
  cleanup(): void {
    this.particles.clear();
    this.emitters.clear();
    this.particleProperties.clear();
    this.particlePhysics.clear();
    this.renderConfigs.clear();
    this.activeEmitters.clear();
    this.particlePool = [];
    this.simulationTime = 0;
  }
}

/**
 * 粒子发射器节点
 */
export class ParticleEmitterNode extends VisualScriptNode {
  public static readonly TYPE = 'ParticleEmitter';
  public static readonly NAME = '粒子发射器';
  public static readonly DESCRIPTION = '创建和控制粒子发射器';

  private static particleSystem: AdvancedParticleSystem = new AdvancedParticleSystem();

  constructor(nodeType: string = ParticleEmitterNode.TYPE, name: string = ParticleEmitterNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('create', 'trigger', '创建');
    this.addInput('start', 'trigger', '启动');
    this.addInput('stop', 'trigger', '停止');
    this.addInput('emitterId', 'string', '发射器ID');
    this.addInput('shape', 'string', '发射器形状');
    this.addInput('rate', 'number', '发射率');
    this.addInput('position', 'object', '位置');
    this.addInput('direction', 'object', '方向');
    this.addInput('speed', 'number', '速度');
    this.addInput('angle', 'number', '发射角度');
    this.addInput('emissionArea', 'object', '发射区域');

    // 输出端口
    this.addOutput('emitterId', 'string', '发射器ID');
    this.addOutput('isActive', 'boolean', '是否活跃');
    this.addOutput('particleCount', 'number', '粒子数量');
    this.addOutput('onCreated', 'trigger', '创建完成');
    this.addOutput('onStarted', 'trigger', '启动完成');
    this.addOutput('onStopped', 'trigger', '停止完成');
  }

  public execute(inputs?: any): any {
    try {
      const createTrigger = inputs?.create;
      const startTrigger = inputs?.start;
      const stopTrigger = inputs?.stop;
      const emitterId = inputs?.emitterId as string || this.generateEmitterId();

      if (createTrigger) {
        return this.createEmitter(inputs, emitterId);
      } else if (startTrigger) {
        return this.startEmitter(emitterId);
      } else if (stopTrigger) {
        return this.stopEmitter(emitterId);
      }

      return this.getDefaultOutputs();

    } catch (error) {
      Debug.error('ParticleEmitterNode', '粒子发射器操作失败', error);
      return this.getDefaultOutputs();
    }
  }

  private createEmitter(inputs: any, emitterId: string): any {
    const shape = inputs?.shape as string || 'point';
    const rate = inputs?.rate as number || 10;
    const position = inputs?.position as Vector3 || new Vector3(0, 0, 0);
    const direction = inputs?.direction as Vector3 || new Vector3(0, 1, 0);
    const speed = inputs?.speed as number || 5;
    const angle = inputs?.angle as number || 0;
    const emissionArea = inputs?.emissionArea as Vector3 || new Vector3(1, 1, 1);

    // 创建发射器配置
    const emitterConfig: EmitterConfig = {
      shape: shape as EmitterShape,
      rate,
      burst: 0,
      duration: Infinity,
      loop: true,
      prewarm: false,
      position,
      rotation: new Vector3(0, 0, 0),
      scale: new Vector3(1, 1, 1),
      emissionArea,
      emissionDirection: direction.normalize(),
      emissionAngle: angle,
      emissionSpeed: speed,
      emissionSpeedVariation: speed * 0.2
    };

    // 创建粒子属性
    const particleProperties: ParticleProperties = {
      type: ParticleType.SPRITE,
      lifetime: 2.0,
      lifetimeVariation: 0.5,
      startSize: 1.0,
      endSize: 0.1,
      sizeVariation: 0.2,
      startColor: new Color(1, 1, 1),
      endColor: new Color(1, 1, 1),
      colorVariation: 0.1,
      startAlpha: 1.0,
      endAlpha: 0.0,
      alphaVariation: 0.1,
      startRotation: 0,
      endRotation: 0,
      rotationVariation: 0,
      mass: 1.0,
      massVariation: 0.2
    };

    // 创建物理配置
    const particlePhysics: ParticlePhysics = {
      gravity: new Vector3(0, -9.81, 0),
      drag: 0.1,
      angularDrag: 0.1,
      bounce: 0.3,
      friction: 0.5,
      collisionEnabled: false,
      collisionLayers: [],
      windInfluence: 0.1,
      turbulence: 0.1,
      turbulenceFrequency: 1.0
    };

    // 创建渲染配置
    const renderConfig: ParticleRenderConfig = {
      blendMode: BlendMode.ADDITIVE,
      depthWrite: false,
      depthTest: true,
      sortParticles: true,
      faceCameraMode: 'free',
      textureAtlas: false,
      atlasRows: 1,
      atlasCols: 1,
      animationSpeed: 1.0
    };

    // 创建发射器
    ParticleEmitterNode.particleSystem.createEmitter(emitterId, emitterConfig, particleProperties, particlePhysics, renderConfig);

    Debug.log('ParticleEmitterNode', `粒子发射器创建成功: ${emitterId}`);

    return {
      emitterId,
      isActive: false,
      particleCount: 0,
      onCreated: true,
      onStarted: false,
      onStopped: false
    };
  }

  private startEmitter(emitterId: string): any {
    ParticleEmitterNode.particleSystem.startEmitter(emitterId);

    return {
      emitterId,
      isActive: true,
      particleCount: ParticleEmitterNode.particleSystem.getParticleCount(),
      onCreated: false,
      onStarted: true,
      onStopped: false
    };
  }

  private stopEmitter(emitterId: string): any {
    ParticleEmitterNode.particleSystem.stopEmitter(emitterId);

    return {
      emitterId,
      isActive: false,
      particleCount: ParticleEmitterNode.particleSystem.getParticleCount(),
      onCreated: false,
      onStarted: false,
      onStopped: true
    };
  }

  private generateEmitterId(): string {
    return 'emitter_' + Math.random().toString(36).substr(2, 9);
  }

  private getDefaultOutputs(): any {
    return {
      emitterId: '',
      isActive: false,
      particleCount: 0,
      onCreated: false,
      onStarted: false,
      onStopped: false
    };
  }
}

/**
 * 粒子效果节点
 */
export class ParticleEffectNode extends VisualScriptNode {
  public static readonly TYPE = 'ParticleEffect';
  public static readonly NAME = '粒子效果';
  public static readonly DESCRIPTION = '创建预设的粒子效果';

  private static particleSystem: AdvancedParticleSystem = new AdvancedParticleSystem();

  constructor(nodeType: string = ParticleEffectNode.TYPE, name: string = ParticleEffectNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('create', 'trigger', '创建');
    this.addInput('effectType', 'string', '效果类型');
    this.addInput('position', 'object', '位置');
    this.addInput('scale', 'number', '缩放');
    this.addInput('intensity', 'number', '强度');
    this.addInput('color', 'object', '颜色');
    this.addInput('duration', 'number', '持续时间');

    // 输出端口
    this.addOutput('effectId', 'string', '效果ID');
    this.addOutput('emitterIds', 'array', '发射器ID列表');
    this.addOutput('isPlaying', 'boolean', '正在播放');
    this.addOutput('onCreated', 'trigger', '创建完成');
    this.addOutput('onCompleted', 'trigger', '播放完成');
  }

  public execute(inputs?: any): any {
    try {
      const createTrigger = inputs?.create;
      if (!createTrigger) {
        return this.getDefaultOutputs();
      }

      const effectType = inputs?.effectType as string || 'explosion';
      const position = inputs?.position as Vector3 || new Vector3(0, 0, 0);
      const scale = inputs?.scale as number || 1.0;
      const intensity = inputs?.intensity as number || 1.0;
      const color = inputs?.color as Color || new Color(1, 0.5, 0);
      const duration = inputs?.duration as number || 2.0;

      const effectResult = this.createEffect(effectType, position, scale, intensity, color, duration);

      return {
        effectId: effectResult.effectId,
        emitterIds: effectResult.emitterIds,
        isPlaying: true,
        onCreated: true,
        onCompleted: false
      };

    } catch (error) {
      Debug.error('ParticleEffectNode', '粒子效果创建失败', error);
      return this.getDefaultOutputs();
    }
  }

  private createEffect(type: string, position: Vector3, scale: number, intensity: number, color: Color, duration: number): any {
    const effectId = this.generateEffectId();
    const emitterIds: string[] = [];

    switch (type) {
      case 'explosion':
        emitterIds.push(...this.createExplosionEffect(effectId, position, scale, intensity, color, duration));
        break;
      case 'fire':
        emitterIds.push(...this.createFireEffect(effectId, position, scale, intensity, color, duration));
        break;
      case 'smoke':
        emitterIds.push(...this.createSmokeEffect(effectId, position, scale, intensity, color, duration));
        break;
      case 'magic':
        emitterIds.push(...this.createMagicEffect(effectId, position, scale, intensity, color, duration));
        break;
      case 'sparks':
        emitterIds.push(...this.createSparksEffect(effectId, position, scale, intensity, color, duration));
        break;
      default:
        emitterIds.push(...this.createBasicEffect(effectId, position, scale, intensity, color, duration));
        break;
    }

    Debug.log('ParticleEffectNode', `粒子效果创建: ${type} (${emitterIds.length}个发射器)`);

    return { effectId, emitterIds };
  }

  private createExplosionEffect(effectId: string, position: Vector3, scale: number, intensity: number, color: Color, duration: number): string[] {
    const emitterIds: string[] = [];

    // 主爆炸发射器
    const mainEmitterId = `${effectId}_explosion_main`;
    this.createExplosionEmitter(mainEmitterId, position, scale, intensity, color, duration);
    emitterIds.push(mainEmitterId);

    // 火花发射器
    const sparksEmitterId = `${effectId}_explosion_sparks`;
    this.createSparksEmitter(sparksEmitterId, position, scale * 0.5, intensity, new Color(1, 0.8, 0), duration * 0.8);
    emitterIds.push(sparksEmitterId);

    return emitterIds;
  }

  private createFireEffect(effectId: string, position: Vector3, scale: number, intensity: number, color: Color, duration: number): string[] {
    const emitterIds: string[] = [];

    // 火焰发射器
    const fireEmitterId = `${effectId}_fire`;
    this.createFireEmitter(fireEmitterId, position, scale, intensity, color, duration);
    emitterIds.push(fireEmitterId);

    return emitterIds;
  }

  private createSmokeEffect(effectId: string, position: Vector3, scale: number, intensity: number, color: Color, duration: number): string[] {
    const emitterIds: string[] = [];

    // 烟雾发射器
    const smokeEmitterId = `${effectId}_smoke`;
    this.createSmokeEmitter(smokeEmitterId, position, scale, intensity, color, duration);
    emitterIds.push(smokeEmitterId);

    return emitterIds;
  }

  private createMagicEffect(effectId: string, position: Vector3, scale: number, intensity: number, color: Color, duration: number): string[] {
    const emitterIds: string[] = [];

    // 魔法粒子发射器
    const magicEmitterId = `${effectId}_magic`;
    this.createMagicEmitter(magicEmitterId, position, scale, intensity, color, duration);
    emitterIds.push(magicEmitterId);

    return emitterIds;
  }

  private createSparksEffect(effectId: string, position: Vector3, scale: number, intensity: number, color: Color, duration: number): string[] {
    const emitterIds: string[] = [];

    // 火花发射器
    const sparksEmitterId = `${effectId}_sparks`;
    this.createSparksEmitter(sparksEmitterId, position, scale, intensity, color, duration);
    emitterIds.push(sparksEmitterId);

    return emitterIds;
  }

  private createBasicEffect(effectId: string, position: Vector3, scale: number, intensity: number, color: Color, duration: number): string[] {
    const emitterIds: string[] = [];

    // 基础发射器
    const basicEmitterId = `${effectId}_basic`;
    this.createBasicEmitter(basicEmitterId, position, scale, intensity, color, duration);
    emitterIds.push(basicEmitterId);

    return emitterIds;
  }

  // 简化的发射器创建方法
  private createExplosionEmitter(id: string, position: Vector3, scale: number, intensity: number, color: Color, duration: number): void {
    // 这里应该创建具体的爆炸发射器配置
    // 简化实现
  }

  private createFireEmitter(id: string, position: Vector3, scale: number, intensity: number, color: Color, duration: number): void {
    // 火焰发射器配置
  }

  private createSmokeEmitter(id: string, position: Vector3, scale: number, intensity: number, color: Color, duration: number): void {
    // 烟雾发射器配置
  }

  private createMagicEmitter(id: string, position: Vector3, scale: number, intensity: number, color: Color, duration: number): void {
    // 魔法发射器配置
  }

  private createSparksEmitter(id: string, position: Vector3, scale: number, intensity: number, color: Color, duration: number): void {
    // 火花发射器配置
  }

  private createBasicEmitter(id: string, position: Vector3, scale: number, intensity: number, color: Color, duration: number): void {
    // 基础发射器配置
  }

  private generateEffectId(): string {
    return 'effect_' + Math.random().toString(36).substr(2, 9);
  }

  private getDefaultOutputs(): any {
    return {
      effectId: '',
      emitterIds: [],
      isPlaying: false,
      onCreated: false,
      onCompleted: false
    };
  }
}
