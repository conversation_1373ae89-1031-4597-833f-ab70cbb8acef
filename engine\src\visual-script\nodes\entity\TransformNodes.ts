/**
 * 变换节点集合
 * 提供位置、旋转、缩放等变换操作的节点
 */
import { VisualScriptNode } from '../../visualscript/VisualScriptNode';
import { Debug } from '../../utils/Debug';
import { Vector3, Quaternion, Euler, Matrix4 } from 'three';
import { Entity } from './EntityNodes';

/**
 * 获取位置节点
 */
export class GetPositionNode extends VisualScriptNode {
  public static readonly TYPE = 'GetPosition';
  public static readonly NAME = '获取位置';
  public static readonly DESCRIPTION = '获取实体的位置信息';

  constructor(nodeType: string = GetPositionNode.TYPE, name: string = GetPositionNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('entity', 'object', '目标实体');
    this.addInput('worldSpace', 'boolean', '世界坐标');

    // 输出端口
    this.addOutput('position', 'object', '位置向量');
    this.addOutput('x', 'number', 'X坐标');
    this.addOutput('y', 'number', 'Y坐标');
    this.addOutput('z', 'number', 'Z坐标');
    this.addOutput('distance', 'number', '距离原点');
  }

  public execute(inputs?: any): any {
    try {
      const entity = inputs?.entity as Entity;
      const worldSpace = inputs?.worldSpace as boolean || false;

      if (!entity) {
        return this.getDefaultOutputs();
      }

      let position = entity.transform.position.clone();

      // 如果需要世界坐标，计算世界位置
      if (worldSpace) {
        position = this.getWorldPosition(entity);
      }

      const distance = position.length();

      return {
        position,
        x: position.x,
        y: position.y,
        z: position.z,
        distance
      };

    } catch (error) {
      Debug.error('GetPositionNode', '获取位置失败', error);
      return this.getDefaultOutputs();
    }
  }

  private getDefaultOutputs(): any {
    return {
      position: new Vector3(0, 0, 0),
      x: 0,
      y: 0,
      z: 0,
      distance: 0
    };
  }

  private getWorldPosition(entity: Entity): Vector3 {
    let worldPosition = entity.transform.position.clone();
    let currentEntity = entity.parent;

    while (currentEntity) {
      worldPosition.add(currentEntity.transform.position);
      currentEntity = currentEntity.parent;
    }

    return worldPosition;
  }
}

/**
 * 设置位置节点
 */
export class SetPositionNode extends VisualScriptNode {
  public static readonly TYPE = 'SetPosition';
  public static readonly NAME = '设置位置';
  public static readonly DESCRIPTION = '设置实体的位置';

  constructor(nodeType: string = SetPositionNode.TYPE, name: string = SetPositionNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('set', 'trigger', '设置');
    this.addInput('entity', 'object', '目标实体');
    this.addInput('position', 'object', '新位置');
    this.addInput('x', 'number', 'X坐标');
    this.addInput('y', 'number', 'Y坐标');
    this.addInput('z', 'number', 'Z坐标');
    this.addInput('worldSpace', 'boolean', '世界坐标');

    // 输出端口
    this.addOutput('entity', 'object', '实体');
    this.addOutput('newPosition', 'object', '新位置');
    this.addOutput('oldPosition', 'object', '旧位置');
    this.addOutput('onSet', 'trigger', '设置完成');
  }

  public execute(inputs?: any): any {
    try {
      const setTrigger = inputs?.set;
      const entity = inputs?.entity as Entity;

      if (!setTrigger || !entity) {
        return this.getDefaultOutputs();
      }

      const oldPosition = entity.transform.position.clone();
      let newPosition: Vector3;

      // 优先使用position输入，否则使用x,y,z
      if (inputs?.position) {
        newPosition = (inputs.position as Vector3).clone();
      } else {
        const x = inputs?.x !== undefined ? inputs.x : entity.transform.position.x;
        const y = inputs?.y !== undefined ? inputs.y : entity.transform.position.y;
        const z = inputs?.z !== undefined ? inputs.z : entity.transform.position.z;
        newPosition = new Vector3(x, y, z);
      }

      const worldSpace = inputs?.worldSpace as boolean || false;

      // 如果是世界坐标，需要转换为本地坐标
      if (worldSpace && entity.parent) {
        newPosition = this.worldToLocalPosition(entity, newPosition);
      }

      entity.transform.position.copy(newPosition);

      Debug.log('SetPositionNode', `位置已设置: ${entity.name} -> (${newPosition.x}, ${newPosition.y}, ${newPosition.z})`);

      return {
        entity,
        newPosition,
        oldPosition,
        onSet: true
      };

    } catch (error) {
      Debug.error('SetPositionNode', '设置位置失败', error);
      return this.getDefaultOutputs();
    }
  }

  private getDefaultOutputs(): any {
    return {
      entity: null,
      newPosition: new Vector3(0, 0, 0),
      oldPosition: new Vector3(0, 0, 0),
      onSet: false
    };
  }

  private worldToLocalPosition(entity: Entity, worldPosition: Vector3): Vector3 {
    let localPosition = worldPosition.clone();
    let currentEntity = entity.parent;

    while (currentEntity) {
      localPosition.sub(currentEntity.transform.position);
      currentEntity = currentEntity.parent;
    }

    return localPosition;
  }
}

/**
 * 移动节点
 */
export class MoveNode extends VisualScriptNode {
  public static readonly TYPE = 'Move';
  public static readonly NAME = '移动';
  public static readonly DESCRIPTION = '移动实体位置';

  constructor(nodeType: string = MoveNode.TYPE, name: string = MoveNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('move', 'trigger', '移动');
    this.addInput('entity', 'object', '目标实体');
    this.addInput('direction', 'object', '移动方向');
    this.addInput('distance', 'number', '移动距离');
    this.addInput('deltaTime', 'number', '时间增量');
    this.addInput('speed', 'number', '移动速度');

    // 输出端口
    this.addOutput('entity', 'object', '实体');
    this.addOutput('newPosition', 'object', '新位置');
    this.addOutput('displacement', 'object', '位移向量');
    this.addOutput('onMoved', 'trigger', '移动完成');
  }

  public execute(inputs?: any): any {
    try {
      const moveTrigger = inputs?.move;
      const entity = inputs?.entity as Entity;

      if (!moveTrigger || !entity) {
        return this.getDefaultOutputs();
      }

      const direction = inputs?.direction as Vector3 || new Vector3(0, 0, 1);
      const distance = inputs?.distance as number;
      const deltaTime = inputs?.deltaTime as number || 0.016; // 默认60fps
      const speed = inputs?.speed as number;

      let displacement: Vector3;

      if (distance !== undefined) {
        // 使用指定距离
        displacement = direction.clone().normalize().multiplyScalar(distance);
      } else if (speed !== undefined) {
        // 使用速度和时间计算位移
        displacement = direction.clone().normalize().multiplyScalar(speed * deltaTime);
      } else {
        // 直接使用方向向量作为位移
        displacement = direction.clone();
      }

      const oldPosition = entity.transform.position.clone();
      entity.transform.position.add(displacement);

      Debug.log('MoveNode', `实体已移动: ${entity.name} 位移 (${displacement.x}, ${displacement.y}, ${displacement.z})`);

      return {
        entity,
        newPosition: entity.transform.position.clone(),
        displacement,
        onMoved: true
      };

    } catch (error) {
      Debug.error('MoveNode', '移动实体失败', error);
      return this.getDefaultOutputs();
    }
  }

  private getDefaultOutputs(): any {
    return {
      entity: null,
      newPosition: new Vector3(0, 0, 0),
      displacement: new Vector3(0, 0, 0),
      onMoved: false
    };
  }
}

/**
 * 获取旋转节点
 */
export class GetRotationNode extends VisualScriptNode {
  public static readonly TYPE = 'GetRotation';
  public static readonly NAME = '获取旋转';
  public static readonly DESCRIPTION = '获取实体的旋转信息';

  constructor(nodeType: string = GetRotationNode.TYPE, name: string = GetRotationNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('entity', 'object', '目标实体');
    this.addInput('asEuler', 'boolean', '欧拉角格式');

    // 输出端口
    this.addOutput('rotation', 'object', '旋转四元数');
    this.addOutput('euler', 'object', '欧拉角');
    this.addOutput('x', 'number', 'X轴旋转');
    this.addOutput('y', 'number', 'Y轴旋转');
    this.addOutput('z', 'number', 'Z轴旋转');
    this.addOutput('w', 'number', 'W分量');
  }

  public execute(inputs?: any): any {
    try {
      const entity = inputs?.entity as Entity;
      const asEuler = inputs?.asEuler as boolean || false;

      if (!entity) {
        return this.getDefaultOutputs();
      }

      const rotation = entity.transform.rotation.clone();
      const euler = new Euler().setFromQuaternion(rotation);

      return {
        rotation,
        euler,
        x: asEuler ? euler.x : rotation.x,
        y: asEuler ? euler.y : rotation.y,
        z: asEuler ? euler.z : rotation.z,
        w: rotation.w
      };

    } catch (error) {
      Debug.error('GetRotationNode', '获取旋转失败', error);
      return this.getDefaultOutputs();
    }
  }

  private getDefaultOutputs(): any {
    return {
      rotation: new Quaternion(0, 0, 0, 1),
      euler: new Euler(0, 0, 0),
      x: 0,
      y: 0,
      z: 0,
      w: 1
    };
  }
}

/**
 * 设置旋转节点
 */
export class SetRotationNode extends VisualScriptNode {
  public static readonly TYPE = 'SetRotation';
  public static readonly NAME = '设置旋转';
  public static readonly DESCRIPTION = '设置实体的旋转';

  constructor(nodeType: string = SetRotationNode.TYPE, name: string = SetRotationNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('set', 'trigger', '设置');
    this.addInput('entity', 'object', '目标实体');
    this.addInput('rotation', 'object', '旋转四元数');
    this.addInput('euler', 'object', '欧拉角');
    this.addInput('x', 'number', 'X轴旋转');
    this.addInput('y', 'number', 'Y轴旋转');
    this.addInput('z', 'number', 'Z轴旋转');

    // 输出端口
    this.addOutput('entity', 'object', '实体');
    this.addOutput('newRotation', 'object', '新旋转');
    this.addOutput('oldRotation', 'object', '旧旋转');
    this.addOutput('onSet', 'trigger', '设置完成');
  }

  public execute(inputs?: any): any {
    try {
      const setTrigger = inputs?.set;
      const entity = inputs?.entity as Entity;

      if (!setTrigger || !entity) {
        return this.getDefaultOutputs();
      }

      const oldRotation = entity.transform.rotation.clone();
      let newRotation: Quaternion;

      // 优先级：rotation > euler > x,y,z
      if (inputs?.rotation) {
        newRotation = (inputs.rotation as Quaternion).clone();
      } else if (inputs?.euler) {
        newRotation = new Quaternion().setFromEuler(inputs.euler as Euler);
      } else {
        const x = inputs?.x !== undefined ? inputs.x : 0;
        const y = inputs?.y !== undefined ? inputs.y : 0;
        const z = inputs?.z !== undefined ? inputs.z : 0;
        newRotation = new Quaternion().setFromEuler(new Euler(x, y, z));
      }

      entity.transform.rotation.copy(newRotation);

      Debug.log('SetRotationNode', `旋转已设置: ${entity.name}`);

      return {
        entity,
        newRotation,
        oldRotation,
        onSet: true
      };

    } catch (error) {
      Debug.error('SetRotationNode', '设置旋转失败', error);
      return this.getDefaultOutputs();
    }
  }

  private getDefaultOutputs(): any {
    return {
      entity: null,
      newRotation: new Quaternion(0, 0, 0, 1),
      oldRotation: new Quaternion(0, 0, 0, 1),
      onSet: false
    };
  }
}

/**
 * 旋转节点
 */
export class RotateNode extends VisualScriptNode {
  public static readonly TYPE = 'Rotate';
  public static readonly NAME = '旋转';
  public static readonly DESCRIPTION = '旋转实体';

  constructor(nodeType: string = RotateNode.TYPE, name: string = RotateNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('rotate', 'trigger', '旋转');
    this.addInput('entity', 'object', '目标实体');
    this.addInput('axis', 'object', '旋转轴');
    this.addInput('angle', 'number', '旋转角度');
    this.addInput('deltaTime', 'number', '时间增量');
    this.addInput('angularSpeed', 'number', '角速度');

    // 输出端口
    this.addOutput('entity', 'object', '实体');
    this.addOutput('newRotation', 'object', '新旋转');
    this.addOutput('deltaRotation', 'object', '旋转增量');
    this.addOutput('onRotated', 'trigger', '旋转完成');
  }

  public execute(inputs?: any): any {
    try {
      const rotateTrigger = inputs?.rotate;
      const entity = inputs?.entity as Entity;

      if (!rotateTrigger || !entity) {
        return this.getDefaultOutputs();
      }

      const axis = inputs?.axis as Vector3 || new Vector3(0, 1, 0);
      const angle = inputs?.angle as number;
      const deltaTime = inputs?.deltaTime as number || 0.016;
      const angularSpeed = inputs?.angularSpeed as number;

      let rotationAngle: number;

      if (angle !== undefined) {
        rotationAngle = angle;
      } else if (angularSpeed !== undefined) {
        rotationAngle = angularSpeed * deltaTime;
      } else {
        rotationAngle = 0;
      }

      const deltaRotation = new Quaternion().setFromAxisAngle(axis.normalize(), rotationAngle);
      const oldRotation = entity.transform.rotation.clone();
      
      entity.transform.rotation.multiplyQuaternions(entity.transform.rotation, deltaRotation);

      Debug.log('RotateNode', `实体已旋转: ${entity.name} 角度 ${rotationAngle} 弧度`);

      return {
        entity,
        newRotation: entity.transform.rotation.clone(),
        deltaRotation,
        onRotated: true
      };

    } catch (error) {
      Debug.error('RotateNode', '旋转实体失败', error);
      return this.getDefaultOutputs();
    }
  }

  private getDefaultOutputs(): any {
    return {
      entity: null,
      newRotation: new Quaternion(0, 0, 0, 1),
      deltaRotation: new Quaternion(0, 0, 0, 1),
      onRotated: false
    };
  }
}

/**
 * 获取缩放节点
 */
export class GetScaleNode extends VisualScriptNode {
  public static readonly TYPE = 'GetScale';
  public static readonly NAME = '获取缩放';
  public static readonly DESCRIPTION = '获取实体的缩放信息';

  constructor(nodeType: string = GetScaleNode.TYPE, name: string = GetScaleNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('entity', 'object', '目标实体');

    // 输出端口
    this.addOutput('scale', 'object', '缩放向量');
    this.addOutput('x', 'number', 'X轴缩放');
    this.addOutput('y', 'number', 'Y轴缩放');
    this.addOutput('z', 'number', 'Z轴缩放');
    this.addOutput('uniform', 'number', '统一缩放');
  }

  public execute(inputs?: any): any {
    try {
      const entity = inputs?.entity as Entity;

      if (!entity) {
        return this.getDefaultOutputs();
      }

      const scale = entity.transform.scale.clone();
      const uniform = (scale.x + scale.y + scale.z) / 3;

      return {
        scale,
        x: scale.x,
        y: scale.y,
        z: scale.z,
        uniform
      };

    } catch (error) {
      Debug.error('GetScaleNode', '获取缩放失败', error);
      return this.getDefaultOutputs();
    }
  }

  private getDefaultOutputs(): any {
    return {
      scale: new Vector3(1, 1, 1),
      x: 1,
      y: 1,
      z: 1,
      uniform: 1
    };
  }
}

/**
 * 设置缩放节点
 */
export class SetScaleNode extends VisualScriptNode {
  public static readonly TYPE = 'SetScale';
  public static readonly NAME = '设置缩放';
  public static readonly DESCRIPTION = '设置实体的缩放';

  constructor(nodeType: string = SetScaleNode.TYPE, name: string = SetScaleNode.NAME, id?: string) {
    super(nodeType, name, id);
    this.setupPorts();
  }

  private setupPorts(): void {
    // 输入端口
    this.addInput('set', 'trigger', '设置');
    this.addInput('entity', 'object', '目标实体');
    this.addInput('scale', 'object', '缩放向量');
    this.addInput('x', 'number', 'X轴缩放');
    this.addInput('y', 'number', 'Y轴缩放');
    this.addInput('z', 'number', 'Z轴缩放');
    this.addInput('uniform', 'number', '统一缩放');

    // 输出端口
    this.addOutput('entity', 'object', '实体');
    this.addOutput('newScale', 'object', '新缩放');
    this.addOutput('oldScale', 'object', '旧缩放');
    this.addOutput('onSet', 'trigger', '设置完成');
  }

  public execute(inputs?: any): any {
    try {
      const setTrigger = inputs?.set;
      const entity = inputs?.entity as Entity;

      if (!setTrigger || !entity) {
        return this.getDefaultOutputs();
      }

      const oldScale = entity.transform.scale.clone();
      let newScale: Vector3;

      // 优先级：scale > uniform > x,y,z
      if (inputs?.scale) {
        newScale = (inputs.scale as Vector3).clone();
      } else if (inputs?.uniform !== undefined) {
        const uniform = inputs.uniform as number;
        newScale = new Vector3(uniform, uniform, uniform);
      } else {
        const x = inputs?.x !== undefined ? inputs.x : entity.transform.scale.x;
        const y = inputs?.y !== undefined ? inputs.y : entity.transform.scale.y;
        const z = inputs?.z !== undefined ? inputs.z : entity.transform.scale.z;
        newScale = new Vector3(x, y, z);
      }

      entity.transform.scale.copy(newScale);

      Debug.log('SetScaleNode', `缩放已设置: ${entity.name} -> (${newScale.x}, ${newScale.y}, ${newScale.z})`);

      return {
        entity,
        newScale,
        oldScale,
        onSet: true
      };

    } catch (error) {
      Debug.error('SetScaleNode', '设置缩放失败', error);
      return this.getDefaultOutputs();
    }
  }

  private getDefaultOutputs(): any {
    return {
      entity: null,
      newScale: new Vector3(1, 1, 1),
      oldScale: new Vector3(1, 1, 1),
      onSet: false
    };
  }
}
